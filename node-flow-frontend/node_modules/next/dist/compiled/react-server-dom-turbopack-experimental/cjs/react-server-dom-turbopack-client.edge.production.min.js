/*
 React
 react-server-dom-turbopack-client.edge.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.
*/
'use strict';var p=require("react-dom"),q={stream:!0};function r(a,b){if(a){var c=a[b[0]];if(a=c[b[2]])c=a.name;else{a=c["*"];if(!a)throw Error('Could not find the module "'+b[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');c=b[2]}return 4===b.length?[a.id,a.chunks,c,1]:[a.id,a.chunks,c]}return b}var t=new Map;
function v(a){var b=globalThis.__next_require__(a);if("function"!==typeof b.then||"fulfilled"===b.status)return null;b.then(function(c){b.status="fulfilled";b.value=c},function(c){b.status="rejected";b.reason=c});return b}function aa(){}
function ba(a){for(var b=a[1],c=[],d=0;d<b.length;d++){var e=b[d],g=t.get(e);if(void 0===g){g=globalThis.__next_chunk_load__(e);c.push(g);var f=t.set.bind(t,e,null);g.then(f,aa);t.set(e,g)}else null!==g&&c.push(g)}return 4===a.length?0===c.length?v(a[0]):Promise.all(c).then(function(){return v(a[0])}):0<c.length?Promise.all(c):null}
function ca(a,b,c){if(null!==a)for(var d=0;d<b.length;d++){var e=c,g=x.current;if(g){var f=g.preinitScript,l=a.prefix+b[d];var h=a.crossOrigin;h="string"===typeof h?"use-credentials"===h?h:"":void 0;f.call(g,l,{crossOrigin:h,nonce:e})}}}var x=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,y=Symbol.for("react.element"),da=Symbol.for("react.lazy"),ea=Symbol.for("react.postpone"),z=Symbol.iterator;
function fa(a){if(null===a||"object"!==typeof a)return null;a=z&&a[z]||a["@@iterator"];return"function"===typeof a?a:null}var ha=Array.isArray,A=Object.getPrototypeOf,ia=Object.prototype,B=new WeakMap;function ja(a){return Number.isFinite(a)?0===a&&-Infinity===1/a?"$-0":a:Infinity===a?"$Infinity":-Infinity===a?"$-Infinity":"$NaN"}
function C(a,b,c,d){function e(h,k){if(null===k)return null;if("object"===typeof k){if("function"===typeof k.then){null===l&&(l=new FormData);f++;var n=g++;k.then(function(m){m=JSON.stringify(m,e);var u=l;u.append(b+n,m);f--;0===f&&c(u)},function(m){d(m)});return"$@"+n.toString(16)}if(ha(k))return k;if(k instanceof FormData){null===l&&(l=new FormData);var w=l;h=g++;var E=b+h+"_";k.forEach(function(m,u){w.append(E+u,m)});return"$K"+h.toString(16)}if(k instanceof Map)return k=JSON.stringify(Array.from(k),
e),null===l&&(l=new FormData),h=g++,l.append(b+h,k),"$Q"+h.toString(16);if(k instanceof Set)return k=JSON.stringify(Array.from(k),e),null===l&&(l=new FormData),h=g++,l.append(b+h,k),"$W"+h.toString(16);if(fa(k))return Array.from(k);h=A(k);if(h!==ia&&(null===h||null!==A(h)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return k}if("string"===typeof k){if("Z"===k[k.length-1]&&this[h]instanceof Date)return"$D"+k;
k="$"===k[0]?"$"+k:k;return k}if("boolean"===typeof k)return k;if("number"===typeof k)return ja(k);if("undefined"===typeof k)return"$undefined";if("function"===typeof k){k=B.get(k);if(void 0!==k)return k=JSON.stringify(k,e),null===l&&(l=new FormData),h=g++,l.set(b+h,k),"$F"+h.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.");}if("symbol"===typeof k){h=k.description;if(Symbol.for(h)!==k)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+
(k.description+") cannot be found among global symbols."));return"$S"+h}if("bigint"===typeof k)return"$n"+k.toString(10);throw Error("Type "+typeof k+" is not supported as an argument to a Server Function.");}var g=1,f=0,l=null;a=JSON.stringify(a,e);null===l?c(a):(l.set(b+"0",a),0===f&&c(l))}var D=new WeakMap;
function ka(a){var b,c,d=new Promise(function(e,g){b=e;c=g});C(a,"",function(e){if("string"===typeof e){var g=new FormData;g.append("0",e);e=g}d.status="fulfilled";d.value=e;b(e)},function(e){d.status="rejected";d.reason=e;c(e)});return d}
function la(a){var b=B.get(this);if(!b)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var c=null;if(null!==b.bound){c=D.get(b);c||(c=ka(b),D.set(b,c));if("rejected"===c.status)throw c.reason;if("fulfilled"!==c.status)throw c;b=c.value;var d=new FormData;b.forEach(function(e,g){d.append("$ACTION_"+a+":"+g,e)});c=d;b="$ACTION_REF_"+a}else b="$ACTION_ID_"+b.id;return{name:b,method:"POST",encType:"multipart/form-data",data:c}}
function F(a,b){var c=B.get(this);if(!c)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(c.id!==a)return!1;var d=c.bound;if(null===d)return 0===b;switch(d.status){case "fulfilled":return d.value.length===b;case "pending":throw d;case "rejected":throw d.reason;default:throw"string"!==typeof d.status&&(d.status="pending",d.then(function(e){d.status="fulfilled";d.value=e},function(e){d.status="rejected";d.reason=e})),d;}}
function G(a,b,c){Object.defineProperties(a,{$$FORM_ACTION:{value:void 0===c?la:function(){var d=B.get(this);if(!d)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var e=d.bound;null===e&&(e=Promise.resolve([]));return c(d.id,e)}},$$IS_SIGNATURE_EQUAL:{value:F},bind:{value:H}});B.set(a,b)}var ma=Function.prototype.bind,na=Array.prototype.slice;
function H(){var a=ma.apply(this,arguments),b=B.get(this);if(b){var c=na.call(arguments,1),d=null;d=null!==b.bound?Promise.resolve(b.bound).then(function(e){return e.concat(c)}):Promise.resolve(c);Object.defineProperties(a,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:F},bind:{value:H}});B.set(a,{id:b.id,bound:d})}return a}function oa(a,b,c){function d(){var e=Array.prototype.slice.call(arguments);return b(a,e)}G(d,{id:a,bound:null},c);return d}
function I(a,b,c,d){this.status=a;this.value=b;this.reason=c;this._response=d}I.prototype=Object.create(Promise.prototype);I.prototype.then=function(a,b){switch(this.status){case "resolved_model":J(this);break;case "resolved_module":K(this)}switch(this.status){case "fulfilled":a(this.value);break;case "pending":case "blocked":case "cyclic":a&&(null===this.value&&(this.value=[]),this.value.push(a));b&&(null===this.reason&&(this.reason=[]),this.reason.push(b));break;default:b(this.reason)}};
function pa(a){switch(a.status){case "resolved_model":J(a);break;case "resolved_module":K(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":throw a;default:throw a.reason;}}function L(a,b){for(var c=0;c<a.length;c++)(0,a[c])(b)}function M(a,b,c){switch(a.status){case "fulfilled":L(b,a.value);break;case "pending":case "blocked":case "cyclic":a.value=b;a.reason=c;break;case "rejected":c&&L(c,a.reason)}}
function N(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.reason;a.status="rejected";a.reason=b;null!==c&&L(c,b)}}function O(a,b){if("pending"===a.status||"blocked"===a.status){var c=a.value,d=a.reason;a.status="resolved_module";a.value=b;null!==c&&(K(a),M(a,c,d))}}var P=null,Q=null;
function J(a){var b=P,c=Q;P=a;Q=null;var d=a.value;a.status="cyclic";a.value=null;a.reason=null;try{var e=JSON.parse(d,a._response._fromJSON);if(null!==Q&&0<Q.deps)Q.value=e,a.status="blocked",a.value=null,a.reason=null;else{var g=a.value;a.status="fulfilled";a.value=e;null!==g&&L(g,e)}}catch(f){a.status="rejected",a.reason=f}finally{P=b,Q=c}}
function K(a){try{var b=a.value,c=globalThis.__next_require__(b[0]);if(4===b.length&&"function"===typeof c.then)if("fulfilled"===c.status)c=c.value;else throw c.reason;var d="*"===b[2]?c:""===b[2]?c.__esModule?c.default:c:c[b[2]];a.status="fulfilled";a.value=d}catch(e){a.status="rejected",a.reason=e}}function R(a,b){a._chunks.forEach(function(c){"pending"===c.status&&N(c,b)})}function S(a,b){var c=a._chunks,d=c.get(b);d||(d=new I("pending",null,null,a),c.set(b,d));return d}
function qa(a,b,c,d){if(Q){var e=Q;d||e.deps++}else e=Q={deps:d?0:1,value:null};return function(g){b[c]=g;e.deps--;0===e.deps&&"blocked"===a.status&&(g=a.value,a.status="fulfilled",a.value=e.value,null!==g&&L(g,e.value))}}function ra(a){return function(b){return N(a,b)}}
function sa(a,b){function c(){var e=Array.prototype.slice.call(arguments),g=b.bound;return g?"fulfilled"===g.status?d(b.id,g.value.concat(e)):Promise.resolve(g).then(function(f){return d(b.id,f.concat(e))}):d(b.id,e)}var d=a._callServer;G(c,b,a._encodeFormAction);return c}function T(a,b){a=S(a,b);switch(a.status){case "resolved_model":J(a)}switch(a.status){case "fulfilled":return a.value;default:throw a.reason;}}
function ta(a,b,c,d){if("$"===d[0]){if("$"===d)return y;switch(d[1]){case "$":return d.slice(1);case "L":return b=parseInt(d.slice(2),16),a=S(a,b),{$$typeof:da,_payload:a,_init:pa};case "@":if(2===d.length)return new Promise(function(){});b=parseInt(d.slice(2),16);return S(a,b);case "S":return Symbol.for(d.slice(2));case "F":return b=parseInt(d.slice(2),16),b=T(a,b),sa(a,b);case "Q":return b=parseInt(d.slice(2),16),a=T(a,b),new Map(a);case "W":return b=parseInt(d.slice(2),16),a=T(a,b),new Set(a);
case "I":return Infinity;case "-":return"$-0"===d?-0:-Infinity;case "N":return NaN;case "u":return;case "D":return new Date(Date.parse(d.slice(2)));case "n":return BigInt(d.slice(2));default:d=parseInt(d.slice(1),16);a=S(a,d);switch(a.status){case "resolved_model":J(a);break;case "resolved_module":K(a)}switch(a.status){case "fulfilled":return a.value;case "pending":case "blocked":case "cyclic":return d=P,a.then(qa(d,b,c,"cyclic"===a.status),ra(d)),null;default:throw a.reason;}}}return d}
function ua(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.');}function U(a,b,c){a._chunks.set(b,new I("fulfilled",c,null,a))}
function va(a,b,c){var d=a._chunks,e=d.get(b);c=JSON.parse(c,a._fromJSON);var g=r(a._bundlerConfig,c);ca(a._moduleLoading,c[1],a._nonce);if(c=ba(g)){if(e){var f=e;f.status="blocked"}else f=new I("blocked",null,null,a),d.set(b,f);c.then(function(){return O(f,g)},function(l){return N(f,l)})}else e?O(e,g):d.set(b,new I("resolved_module",g,null,a))}
function V(a,b){for(var c=a.length,d=b.length,e=0;e<c;e++)d+=a[e].byteLength;d=new Uint8Array(d);for(var g=e=0;g<c;g++){var f=a[g];d.set(f,e);e+=f.byteLength}d.set(b,e);return d}function W(a,b,c,d,e,g){c=0===c.length&&0===d.byteOffset%g?d:V(c,d);e=new e(c.buffer,c.byteOffset,c.byteLength/g);U(a,b,e)}
function wa(a,b,c,d,e){switch(c){case 65:U(a,b,V(d,e).buffer);return;case 67:W(a,b,d,e,Int8Array,1);return;case 99:U(a,b,0===d.length?e:V(d,e));return;case 85:W(a,b,d,e,Uint8ClampedArray,1);return;case 83:W(a,b,d,e,Int16Array,2);return;case 115:W(a,b,d,e,Uint16Array,2);return;case 76:W(a,b,d,e,Int32Array,4);return;case 108:W(a,b,d,e,Uint32Array,4);return;case 70:W(a,b,d,e,Float32Array,4);return;case 100:W(a,b,d,e,Float64Array,8);return;case 78:W(a,b,d,e,BigInt64Array,8);return;case 109:W(a,b,d,e,
BigUint64Array,8);return;case 86:W(a,b,d,e,DataView,1);return}for(var g=a._stringDecoder,f="",l=0;l<d.length;l++)f+=g.decode(d[l],q);f+=g.decode(e);switch(c){case 73:va(a,b,f);break;case 72:b=f[0];f=f.slice(1);a=JSON.parse(f,a._fromJSON);if(f=x.current)switch(b){case "D":f.prefetchDNS(a);break;case "C":"string"===typeof a?f.preconnect(a):f.preconnect(a[0],a[1]);break;case "L":b=a[0];c=a[1];3===a.length?f.preload(b,c,a[2]):f.preload(b,c);break;case "m":"string"===typeof a?f.preloadModule(a):f.preloadModule(a[0],
a[1]);break;case "S":"string"===typeof a?f.preinitStyle(a):f.preinitStyle(a[0],0===a[1]?void 0:a[1],3===a.length?a[2]:void 0);break;case "X":"string"===typeof a?f.preinitScript(a):f.preinitScript(a[0],a[1]);break;case "M":"string"===typeof a?f.preinitModuleScript(a):f.preinitModuleScript(a[0],a[1])}break;case 69:c=JSON.parse(f).digest;f=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.");
f.stack="Error: "+f.message;f.digest=c;c=a._chunks;(d=c.get(b))?N(d,f):c.set(b,new I("rejected",null,f,a));break;case 84:a._chunks.set(b,new I("fulfilled",f,null,a));break;case 68:case 87:throw Error("Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.");case 80:f=Error("A Server Component was postponed. The reason is omitted in production builds to avoid leaking sensitive details.");
f.$$typeof=ea;f.stack="Error: "+f.message;c=a._chunks;(d=c.get(b))?N(d,f):c.set(b,new I("rejected",null,f,a));break;default:d=a._chunks,(c=d.get(b))?"pending"===c.status&&(a=c.value,b=c.reason,c.status="resolved_model",c.value=f,null!==a&&(J(c),M(c,a,b))):d.set(b,new I("resolved_model",f,null,a))}}function xa(a){return function(b,c){return"string"===typeof c?ta(a,this,b,c):"object"===typeof c&&null!==c?(b=c[0]===y?{$$typeof:y,type:c[1],key:c[2],ref:null,props:c[3],_owner:null}:c,b):c}}
function X(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.");}
function Y(a){var b=a.ssrManifest.moduleMap,c=a.ssrManifest.moduleLoading,d=a.encodeFormAction;a="string"===typeof a.nonce?a.nonce:void 0;var e=new Map;b={_bundlerConfig:b,_moduleLoading:c,_callServer:void 0!==X?X:ua,_encodeFormAction:d,_nonce:a,_chunks:e,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]};b._fromJSON=xa(b);return b}
function Z(a,b){function c(g){var f=g.value;if(g.done)R(a,Error("Connection closed."));else{var l=0,h=a._rowState;g=a._rowID;for(var k=a._rowTag,n=a._rowLength,w=a._buffer,E=f.length;l<E;){var m=-1;switch(h){case 0:m=f[l++];58===m?h=1:g=g<<4|(96<m?m-87:m-48);continue;case 1:h=f[l];84===h||65===h||67===h||99===h||85===h||83===h||115===h||76===h||108===h||70===h||100===h||78===h||109===h||86===h?(k=h,h=2,l++):64<h&&91>h?(k=h,h=3,l++):(k=0,h=3);continue;case 2:m=f[l++];44===m?h=4:n=n<<4|(96<m?m-87:m-
48);continue;case 3:m=f.indexOf(10,l);break;case 4:m=l+n,m>f.length&&(m=-1)}var u=f.byteOffset+l;if(-1<m)n=new Uint8Array(f.buffer,u,m-l),wa(a,g,k,w,n),l=m,3===h&&l++,n=g=k=h=0,w.length=0;else{f=new Uint8Array(f.buffer,u,f.byteLength-l);w.push(f);n-=f.byteLength;break}}a._rowState=h;a._rowID=g;a._rowTag=k;a._rowLength=n;return e.read().then(c).catch(d)}}function d(g){R(a,g)}var e=b.getReader();e.read().then(c).catch(d)}
exports.createFromFetch=function(a,b){var c=Y(b);a.then(function(d){Z(c,d.body)},function(d){R(c,d)});return S(c,0)};exports.createFromReadableStream=function(a,b){b=Y(b);Z(b,a);return S(b,0)};exports.createServerReference=function(a){return oa(a,X)};exports.encodeReply=function(a){return new Promise(function(b,c){C(a,"",b,c)})};

//# sourceMappingURL=react-server-dom-turbopack-client.edge.production.min.js.map
