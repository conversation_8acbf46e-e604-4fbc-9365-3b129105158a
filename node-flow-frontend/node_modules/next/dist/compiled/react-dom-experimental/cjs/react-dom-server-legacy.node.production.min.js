/*
 React
 react-dom-server-legacy.node.production.min.js

 Copyright (c) Meta Platforms, Inc. and affiliates.

 This source code is licensed under the MIT license found in the
 LICENSE file in the root directory of this source tree.


 JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)

 Copyright (c) 2011 Gary Court
 Permission is hereby granted, free of charge, to any person obtaining a copy
 of this software and associated documentation files (the "Software"), to deal
 in the Software without restriction, including without limitation the rights
 to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 copies of the Software, and to permit persons to whom the Software is
 furnished to do so, subject to the following conditions:

 The above copyright notice and this permission notice shall be included in
 all copies or substantial portions of the Software.

 THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 SOFTWARE.
*/
'use strict';var aa=require("next/dist/compiled/react-experimental"),ba=require("react-dom"),ea=require("stream"),fa=Symbol.for("react.element"),ha=Symbol.for("react.portal"),oa=Symbol.for("react.fragment"),pa=Symbol.for("react.strict_mode"),qa=Symbol.for("react.profiler"),za=Symbol.for("react.provider"),Aa=Symbol.for("react.consumer"),Ba=Symbol.for("react.context"),Ca=Symbol.for("react.forward_ref"),Da=Symbol.for("react.suspense"),Ka=Symbol.for("react.suspense_list"),La=Symbol.for("react.memo"),Ma=Symbol.for("react.lazy"),Na=Symbol.for("react.scope"),
Oa=Symbol.for("react.debug_trace_mode"),Pa=Symbol.for("react.offscreen"),Ya=Symbol.for("react.legacy_hidden"),eb=Symbol.for("react.cache"),fb=Symbol.for("react.memo_cache_sentinel"),gb=Symbol.for("react.postpone"),hb=Symbol.iterator,ib=Array.isArray;
function jb(a,b){var c=a.length&3;var d=a.length-c;var e=b;for(b=0;b<d;){var f=a.charCodeAt(b)&255|(a.charCodeAt(++b)&255)<<8|(a.charCodeAt(++b)&255)<<16|(a.charCodeAt(++b)&255)<<24;++b;f=***********(f&65535)+((***********(f>>>16)&65535)<<16)&**********;f=f<<15|f>>>17;f=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&**********;e^=f;e=e<<13|e>>>19;e=5*(e&65535)+((5*(e>>>16)&65535)<<16)&**********;e=(e&65535)+27492+(((e>>>16)+58964&65535)<<16)}f=0;switch(c){case 3:f^=(a.charCodeAt(b+2)&255)<<
16;case 2:f^=(a.charCodeAt(b+1)&255)<<8;case 1:f^=a.charCodeAt(b)&255,f=***********(f&65535)+((***********(f>>>16)&65535)<<16)&**********,f=f<<15|f>>>17,e^=461845907*(f&65535)+((461845907*(f>>>16)&65535)<<16)&**********}e^=a.length;e^=e>>>16;e=2246822507*(e&65535)+((2246822507*(e>>>16)&65535)<<16)&**********;e^=e>>>13;e=3266489909*(e&65535)+((3266489909*(e>>>16)&65535)<<16)&**********;return(e^e>>>16)>>>0}
var u=Object.assign,z=Object.prototype.hasOwnProperty,kb=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),lb={},mb={};
function nb(a){if(z.call(mb,a))return!0;if(z.call(lb,a))return!1;if(kb.test(a))return mb[a]=!0;lb[a]=!0;return!1}
var ob=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" ")),pb=
new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],
["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical",
"glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering",
"shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],
["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],
["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),qb=/["'&<>]/;
function B(a){if("boolean"===typeof a||"number"===typeof a)return""+a;a=""+a;var b=qb.exec(a);if(b){var c="",d,e=0;for(d=b.index;d<a.length;d++){switch(a.charCodeAt(d)){case 34:b="&quot;";break;case 38:b="&amp;";break;case 39:b="&#x27;";break;case 60:b="&lt;";break;case 62:b="&gt;";break;default:continue}e!==d&&(c+=a.slice(e,d));e=d+1;c+=b}a=e!==d?c+a.slice(e,d):c}return a}
var Ib=/([A-Z])/g,Jb=/^ms-/,Kb=aa.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Lb={pending:!1,data:null,method:null,action:null},Mb=ba.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,Ub={prefetchDNS:Nb,preconnect:Ob,preload:Pb,preloadModule:Qb,preinitStyle:Rb,preinitScript:Sb,preinitModuleScript:Tb},D=[],Vb=/(<\/|<)(s)(cript)/gi;function Wb(a,b,c,d){return""+b+("s"===c?"\\u0073":"\\u0053")+d}
function Xb(a,b,c,d,e){var f=0;void 0!==b&&(f=1);return{idPrefix:void 0===a?"":a,nextFormID:0,streamingFormat:f,bootstrapScriptContent:c,bootstrapScripts:d,bootstrapModules:e,instructions:0,hasBody:!1,hasHtml:!1,unknownResources:{},dnsResources:{},connectResources:{default:{},anonymous:{},credentials:{}},imageResources:{},styleResources:{},scriptResources:{},moduleUnknownResources:{},moduleScriptResources:{}}}function H(a,b,c){return{insertionMode:a,selectedValue:b,tagScope:c}}
function Yb(a){return H("http://www.w3.org/2000/svg"===a?3:"http://www.w3.org/1998/Math/MathML"===a?4:0,null,0)}
function Zb(a,b,c){switch(b){case "noscript":return H(2,null,a.tagScope|1);case "select":return H(2,null!=c.value?c.value:c.defaultValue,a.tagScope);case "svg":return H(3,null,a.tagScope);case "picture":return H(2,null,a.tagScope|2);case "math":return H(4,null,a.tagScope);case "foreignObject":return H(2,null,a.tagScope);case "table":return H(5,null,a.tagScope);case "thead":case "tbody":case "tfoot":return H(6,null,a.tagScope);case "colgroup":return H(8,null,a.tagScope);case "tr":return H(7,null,a.tagScope)}return 5<=
a.insertionMode?H(2,null,a.tagScope):0===a.insertionMode?"html"===b?H(1,null,a.tagScope):H(2,null,a.tagScope):1===a.insertionMode?H(2,null,a.tagScope):a}var $b=new Map;
function ac(a,b){if("object"!==typeof b)throw Error("The `style` prop expects a mapping from style properties to values, not a string. For example, style={{marginRight: spacing + 'em'}} when using JSX.");var c=!0,d;for(d in b)if(z.call(b,d)){var e=b[d];if(null!=e&&"boolean"!==typeof e&&""!==e){if(0===d.indexOf("--")){var f=B(d);e=B((""+e).trim())}else f=$b.get(d),void 0===f&&(f=B(d.replace(Ib,"-$1").toLowerCase().replace(Jb,"-ms-")),$b.set(d,f)),e="number"===typeof e?0===e||ob.has(d)?""+e:e+"px":
B((""+e).trim());c?(c=!1,a.push(' style="',f,":",e)):a.push(";",f,":",e)}}c||a.push('"')}function bc(a,b,c){c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""')}function J(a,b,c){"function"!==typeof c&&"symbol"!==typeof c&&"boolean"!==typeof c&&a.push(" ",b,'="',B(c),'"')}function cc(a){var b=a.nextFormID++;return a.idPrefix+b}var dc=B("javascript:throw new Error('React form unexpectedly submitted.')");
function pc(a,b){this.push('<input type="hidden"');if("string"!==typeof a)throw Error("File/Blob fields are not yet supported in progressive forms. It probably means you are closing over binary data or FormData in a Server Action.");J(this,"name",b);J(this,"value",a);this.push("/>")}
function qc(a,b,c,d,e,f,g,h){var k=null;"function"===typeof d&&("function"===typeof d.$$FORM_ACTION?(e=cc(b),b=d.$$FORM_ACTION(e),h=b.name,d=b.action||"",e=b.encType,f=b.method,g=b.target,k=b.data):(a.push(" ","formAction",'="',dc,'"'),g=f=e=d=h=null,rc(b,c)));null!=h&&K(a,"name",h);null!=d&&K(a,"formAction",d);null!=e&&K(a,"formEncType",e);null!=f&&K(a,"formMethod",f);null!=g&&K(a,"formTarget",g);return k}
function K(a,b,c){switch(b){case "className":J(a,"class",c);break;case "tabIndex":J(a,"tabindex",c);break;case "dir":case "role":case "viewBox":case "width":case "height":J(a,b,c);break;case "style":ac(a,c);break;case "src":case "href":if(""===c)break;case "action":case "formAction":if(null==c||"function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ",b,'="',B(""+c),'"');break;case "defaultValue":case "defaultChecked":case "innerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "ref":break;
case "autoFocus":case "multiple":case "muted":bc(a,b.toLowerCase(),c);break;case "xlinkHref":if("function"===typeof c||"symbol"===typeof c||"boolean"===typeof c)break;a.push(" ","xlink:href",'="',B(""+c),'"');break;case "contentEditable":case "spellCheck":case "draggable":case "value":case "autoReverse":case "externalResourcesRequired":case "focusable":case "preserveAlpha":"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',B(c),'"');break;case "allowFullScreen":case "async":case "autoPlay":case "controls":case "default":case "defer":case "disabled":case "disablePictureInPicture":case "disableRemotePlayback":case "formNoValidate":case "hidden":case "loop":case "noModule":case "noValidate":case "open":case "playsInline":case "readOnly":case "required":case "reversed":case "scoped":case "seamless":case "itemScope":c&&
"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'=""');break;case "capture":case "download":!0===c?a.push(" ",b,'=""'):!1!==c&&"function"!==typeof c&&"symbol"!==typeof c&&a.push(" ",b,'="',B(c),'"');break;case "cols":case "rows":case "size":case "span":"function"!==typeof c&&"symbol"!==typeof c&&!isNaN(c)&&1<=c&&a.push(" ",b,'="',B(c),'"');break;case "rowSpan":case "start":"function"===typeof c||"symbol"===typeof c||isNaN(c)||a.push(" ",b,'="',B(c),'"');break;case "xlinkActuate":J(a,"xlink:actuate",
c);break;case "xlinkArcrole":J(a,"xlink:arcrole",c);break;case "xlinkRole":J(a,"xlink:role",c);break;case "xlinkShow":J(a,"xlink:show",c);break;case "xlinkTitle":J(a,"xlink:title",c);break;case "xlinkType":J(a,"xlink:type",c);break;case "xmlBase":J(a,"xml:base",c);break;case "xmlLang":J(a,"xml:lang",c);break;case "xmlSpace":J(a,"xml:space",c);break;default:if(!(2<b.length)||"o"!==b[0]&&"O"!==b[0]||"n"!==b[1]&&"N"!==b[1])if(b=pb.get(b)||b,nb(b)){switch(typeof c){case "function":case "symbol":return;
case "boolean":var d=b.toLowerCase().slice(0,5);if("data-"!==d&&"aria-"!==d)return}a.push(" ",b,'="',B(c),'"')}}}function L(a,b,c){if(null!=b){if(null!=c)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof b||!("__html"in b))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");b=b.__html;null!==b&&void 0!==b&&a.push(""+b)}}
function sc(a){var b="";aa.Children.forEach(a,function(c){null!=c&&(b+=c)});return b}
function rc(a,b){0!==(a.instructions&16)||b.externalRuntimeScript||(a.instructions|=16,b.bootstrapChunks.unshift(b.startInlineScript,'addEventListener("submit",function(a){if(!a.defaultPrevented){var c=a.target,d=a.submitter,e=c.action,b=d;if(d){var f=d.getAttribute("formAction");null!=f&&(e=f,b=null)}"javascript:throw new Error(\'React form unexpectedly submitted.\')"===e&&(a.preventDefault(),b?(a=document.createElement("input"),a.name=b.name,a.value=b.value,b.parentNode.insertBefore(a,b),b=new FormData(c),a.parentNode.removeChild(a)):b=new FormData(c),a=c.ownerDocument||c,(a.$$reactFormReplay=a.$$reactFormReplay||[]).push(c,d,b))}});',"\x3c/script>"))}
function O(a,b){a.push(P("link"));for(var c in b)if(z.call(b,c)){var d=b[c];if(null!=d)switch(c){case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:K(a,c,d)}}a.push("/>");return null}
function tc(a,b,c){a.push(P(c));for(var d in b)if(z.call(b,d)){var e=b[d];if(null!=e)switch(d){case "children":case "dangerouslySetInnerHTML":throw Error(c+" is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");default:K(a,d,e)}}a.push("/>");return null}
function uc(a,b){a.push(P("title"));var c=null,d=null,e;for(e in b)if(z.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:K(a,e,f)}}a.push(">");b=Array.isArray(c)?2>c.length?c[0]:null:c;"function"!==typeof b&&"symbol"!==typeof b&&null!==b&&void 0!==b&&a.push(B(""+b));L(a,d,c);a.push(vc("title"));return null}
function wc(a,b){a.push(P("script"));var c=null,d=null,e;for(e in b)if(z.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:K(a,e,f)}}a.push(">");L(a,d,c);"string"===typeof c&&a.push(B(c));a.push(vc("script"));return null}
function xc(a,b,c){a.push(P(c));var d=c=null,e;for(e in b)if(z.call(b,e)){var f=b[e];if(null!=f)switch(e){case "children":c=f;break;case "dangerouslySetInnerHTML":d=f;break;default:K(a,e,f)}}a.push(">");L(a,d,c);return"string"===typeof c?(a.push(B(c)),null):c}var yc=/^[a-zA-Z][a-zA-Z:_\.\-\d]*$/,zc=new Map;function P(a){var b=zc.get(a);if(void 0===b){if(!yc.test(a))throw Error("Invalid tag: "+a);b="<"+a;zc.set(a,b)}return b}
function Ac(a,b,c,d,e,f,g,h,k){switch(b){case "div":case "span":case "svg":case "path":break;case "a":a.push(P("a"));var m=null,n=null,l;for(l in c)if(z.call(c,l)){var q=c[l];if(null!=q)switch(l){case "children":m=q;break;case "dangerouslySetInnerHTML":n=q;break;case "href":""===q?J(a,"href",""):K(a,l,q);break;default:K(a,l,q)}}a.push(">");L(a,n,m);if("string"===typeof m){a.push(B(m));var r=null}else r=m;return r;case "g":case "p":case "li":break;case "select":a.push(P("select"));var C=null,v=null,
x;for(x in c)if(z.call(c,x)){var w=c[x];if(null!=w)switch(x){case "children":C=w;break;case "dangerouslySetInnerHTML":v=w;break;case "defaultValue":case "value":break;default:K(a,x,w)}}a.push(">");L(a,v,C);return C;case "option":var p=g.selectedValue;a.push(P("option"));var F=null,E=null,t=null,y=null,A;for(A in c)if(z.call(c,A)){var G=c[A];if(null!=G)switch(A){case "children":F=G;break;case "selected":t=G;break;case "dangerouslySetInnerHTML":y=G;break;case "value":E=G;default:K(a,A,G)}}if(null!=
p){var rb=null!==E?""+E:sc(F);if(ib(p))for(var ra=0;ra<p.length;ra++){if(""+p[ra]===rb){a.push(' selected=""');break}}else""+p===rb&&a.push(' selected=""')}else t&&a.push(' selected=""');a.push(">");L(a,y,F);return F;case "textarea":a.push(P("textarea"));var Q=null,ca=null,S=null,sa;for(sa in c)if(z.call(c,sa)){var ta=c[sa];if(null!=ta)switch(sa){case "children":S=ta;break;case "value":Q=ta;break;case "defaultValue":ca=ta;break;case "dangerouslySetInnerHTML":throw Error("`dangerouslySetInnerHTML` does not make sense on <textarea>.");
default:K(a,sa,ta)}}null===Q&&null!==ca&&(Q=ca);a.push(">");if(null!=S){if(null!=Q)throw Error("If you supply `defaultValue` on a <textarea>, do not pass children.");if(ib(S)){if(1<S.length)throw Error("<textarea> can only have at most one child.");Q=""+S[0]}Q=""+S}"string"===typeof Q&&"\n"===Q[0]&&a.push("\n");null!==Q&&a.push(B(""+Q));return null;case "input":a.push(P("input"));var sb=null,tb=null,ub=null,vb=null,wb=null,ia=null,Y=null,ua=null,ja=null,ka;for(ka in c)if(z.call(c,ka)){var M=c[ka];
if(null!=M)switch(ka){case "children":case "dangerouslySetInnerHTML":throw Error("input is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");case "name":sb=M;break;case "formAction":tb=M;break;case "formEncType":ub=M;break;case "formMethod":vb=M;break;case "formTarget":wb=M;break;case "defaultChecked":ja=M;break;case "defaultValue":Y=M;break;case "checked":ua=M;break;case "value":ia=M;break;default:K(a,ka,M)}}var md=qc(a,d,e,tb,ub,vb,wb,sb);null!==ua?bc(a,"checked",
ua):null!==ja&&bc(a,"checked",ja);null!==ia?K(a,"value",ia):null!==Y&&K(a,"value",Y);a.push("/>");null!==md&&md.forEach(pc,a);return null;case "button":a.push(P("button"));var Qa=null,nd=null,od=null,pd=null,qd=null,rd=null,sd=null,Ra;for(Ra in c)if(z.call(c,Ra)){var da=c[Ra];if(null!=da)switch(Ra){case "children":Qa=da;break;case "dangerouslySetInnerHTML":nd=da;break;case "name":od=da;break;case "formAction":pd=da;break;case "formEncType":qd=da;break;case "formMethod":rd=da;break;case "formTarget":sd=
da;break;default:K(a,Ra,da)}}var td=qc(a,d,e,pd,qd,rd,sd,od);a.push(">");null!==td&&td.forEach(pc,a);L(a,nd,Qa);if("string"===typeof Qa){a.push(B(Qa));var ud=null}else ud=Qa;return ud;case "form":a.push(P("form"));var Sa=null,vd=null,la=null,Ta=null,Ua=null,Va=null,Wa;for(Wa in c)if(z.call(c,Wa)){var ma=c[Wa];if(null!=ma)switch(Wa){case "children":Sa=ma;break;case "dangerouslySetInnerHTML":vd=ma;break;case "action":la=ma;break;case "encType":Ta=ma;break;case "method":Ua=ma;break;case "target":Va=
ma;break;default:K(a,Wa,ma)}}var ec=null,fc=null;if("function"===typeof la)if("function"===typeof la.$$FORM_ACTION){var jf=cc(d),Ea=la.$$FORM_ACTION(jf);la=Ea.action||"";Ta=Ea.encType;Ua=Ea.method;Va=Ea.target;ec=Ea.data;fc=Ea.name}else a.push(" ","action",'="',dc,'"'),Va=Ua=Ta=la=null,rc(d,e);null!=la&&K(a,"action",la);null!=Ta&&K(a,"encType",Ta);null!=Ua&&K(a,"method",Ua);null!=Va&&K(a,"target",Va);a.push(">");null!==fc&&(a.push('<input type="hidden"'),J(a,"name",fc),a.push("/>"),null!==ec&&ec.forEach(pc,
a));L(a,vd,Sa);if("string"===typeof Sa){a.push(B(Sa));var wd=null}else wd=Sa;return wd;case "menuitem":a.push(P("menuitem"));for(var xb in c)if(z.call(c,xb)){var xd=c[xb];if(null!=xd)switch(xb){case "children":case "dangerouslySetInnerHTML":throw Error("menuitems cannot have `children` nor `dangerouslySetInnerHTML`.");default:K(a,xb,xd)}}a.push(">");return null;case "title":if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var gc=uc(a,c);else k?gc=null:(uc(e.hoistableChunks,c),gc=void 0);return gc;
case "link":var kf=c.rel,na=c.href,yb=c.precedence;if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp||"string"!==typeof kf||"string"!==typeof na||""===na){O(a,c);var Xa=null}else if("stylesheet"===c.rel)if("string"!==typeof yb||null!=c.disabled||c.onLoad||c.onError)Xa=O(a,c);else{var Fa=e.styles.get(yb),zb=d.styleResources.hasOwnProperty(na)?d.styleResources[na]:void 0;if(null!==zb){d.styleResources[na]=null;Fa||(Fa={precedence:B(yb),rules:[],hrefs:[],sheets:new Map},e.styles.set(yb,Fa));var Ab=
{state:0,props:u({},c,{"data-precedence":c.precedence,precedence:null})};if(zb){2===zb.length&&Bc(Ab.props,zb);var hc=e.preloads.stylesheets.get(na);hc&&0<hc.length?hc.length=0:Ab.state=1}Fa.sheets.set(na,Ab);f&&f.stylesheets.add(Ab)}else if(Fa){var yd=Fa.sheets.get(na);yd&&f&&f.stylesheets.add(yd)}h&&a.push("\x3c!-- --\x3e");Xa=null}else c.onLoad||c.onError?Xa=O(a,c):(h&&a.push("\x3c!-- --\x3e"),Xa=k?null:O(e.hoistableChunks,c));return Xa;case "script":var ic=c.async;if("string"!==typeof c.src||
!c.src||!ic||"function"===typeof ic||"symbol"===typeof ic||c.onLoad||c.onError||3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var zd=wc(a,c);else{var Bb=c.src;if("module"===c.type){var Cb=d.moduleScriptResources;var Ad=e.preloads.moduleScripts}else Cb=d.scriptResources,Ad=e.preloads.scripts;var Db=Cb.hasOwnProperty(Bb)?Cb[Bb]:void 0;if(null!==Db){Cb[Bb]=null;var jc=c;if(Db){2===Db.length&&(jc=u({},c),Bc(jc,Db));var Bd=Ad.get(Bb);Bd&&(Bd.length=0)}var Cd=[];e.scripts.add(Cd);wc(Cd,jc)}h&&a.push("\x3c!-- --\x3e");
zd=null}return zd;case "style":var Eb=c.precedence,va=c.href;if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp||"string"!==typeof Eb||"string"!==typeof va||""===va){a.push(P("style"));var Ga=null,Dd=null,Za;for(Za in c)if(z.call(c,Za)){var Fb=c[Za];if(null!=Fb)switch(Za){case "children":Ga=Fb;break;case "dangerouslySetInnerHTML":Dd=Fb;break;default:K(a,Za,Fb)}}a.push(">");var $a=Array.isArray(Ga)?2>Ga.length?Ga[0]:null:Ga;"function"!==typeof $a&&"symbol"!==typeof $a&&null!==$a&&void 0!==$a&&
a.push(B(""+$a));L(a,Dd,Ga);a.push(vc("style"));var Ed=null}else{var wa=e.styles.get(Eb);if(null!==(d.styleResources.hasOwnProperty(va)?d.styleResources[va]:void 0)){d.styleResources[va]=null;wa?wa.hrefs.push(B(va)):(wa={precedence:B(Eb),rules:[],hrefs:[B(va)],sheets:new Map},e.styles.set(Eb,wa));var Fd=wa.rules,Ha=null,Gd=null,Gb;for(Gb in c)if(z.call(c,Gb)){var kc=c[Gb];if(null!=kc)switch(Gb){case "children":Ha=kc;break;case "dangerouslySetInnerHTML":Gd=kc}}var ab=Array.isArray(Ha)?2>Ha.length?
Ha[0]:null:Ha;"function"!==typeof ab&&"symbol"!==typeof ab&&null!==ab&&void 0!==ab&&Fd.push(B(""+ab));L(Fd,Gd,Ha)}wa&&f&&f.styles.add(wa);h&&a.push("\x3c!-- --\x3e");Ed=void 0}return Ed;case "meta":if(3===g.insertionMode||g.tagScope&1||null!=c.itemProp)var Hd=tc(a,c,"meta");else h&&a.push("\x3c!-- --\x3e"),Hd=k?null:"string"===typeof c.charSet?tc(e.charsetChunks,c,"meta"):"viewport"===c.name?tc(e.viewportChunks,c,"meta"):tc(e.hoistableChunks,c,"meta");return Hd;case "listing":case "pre":a.push(P(b));
var bb=null,cb=null,db;for(db in c)if(z.call(c,db)){var Hb=c[db];if(null!=Hb)switch(db){case "children":bb=Hb;break;case "dangerouslySetInnerHTML":cb=Hb;break;default:K(a,db,Hb)}}a.push(">");if(null!=cb){if(null!=bb)throw Error("Can only set one of `children` or `props.dangerouslySetInnerHTML`.");if("object"!==typeof cb||!("__html"in cb))throw Error("`props.dangerouslySetInnerHTML` must be in the form `{__html: ...}`. Please visit https://reactjs.org/link/dangerously-set-inner-html for more information.");
var xa=cb.__html;null!==xa&&void 0!==xa&&("string"===typeof xa&&0<xa.length&&"\n"===xa[0]?a.push("\n",xa):a.push(""+xa))}"string"===typeof bb&&"\n"===bb[0]&&a.push("\n");return bb;case "img":var N=c.src,I=c.srcSet;if(!("lazy"===c.loading||!N&&!I||"string"!==typeof N&&null!=N||"string"!==typeof I&&null!=I)&&"low"!==c.fetchPriority&&!1===!!(g.tagScope&2)&&("string"!==typeof N||":"!==N[4]||"d"!==N[0]&&"D"!==N[0]||"a"!==N[1]&&"A"!==N[1]||"t"!==N[2]&&"T"!==N[2]||"a"!==N[3]&&"A"!==N[3])&&("string"!==typeof I||
":"!==I[4]||"d"!==I[0]&&"D"!==I[0]||"a"!==I[1]&&"A"!==I[1]||"t"!==I[2]&&"T"!==I[2]||"a"!==I[3]&&"A"!==I[3])){var Id="string"===typeof c.sizes?c.sizes:void 0,Ia=I?I+"\n"+(Id||""):N,lc=e.preloads.images,ya=lc.get(Ia);if(ya){if("high"===c.fetchPriority||10>e.highImagePreloads.size)lc.delete(Ia),e.highImagePreloads.add(ya)}else if(!d.imageResources.hasOwnProperty(Ia)){d.imageResources[Ia]=D;var mc=c.crossOrigin;var Jd="string"===typeof mc?"use-credentials"===mc?mc:"":void 0;var Z=e.headers,nc;Z&&0<Z.remainingCapacity&&
("high"===c.fetchPriority||500>Z.highImagePreloads.length)&&(nc=Cc(N,"image",{imageSrcSet:c.srcSet,imageSizes:c.sizes,crossOrigin:Jd,integrity:c.integrity,nonce:c.nonce,type:c.type,fetchPriority:c.fetchPriority,referrerPolicy:c.refererPolicy}),2<=(Z.remainingCapacity-=nc.length))?(e.resets.image[Ia]=D,Z.highImagePreloads&&(Z.highImagePreloads+=", "),Z.highImagePreloads+=nc):(ya=[],O(ya,{rel:"preload",as:"image",href:I?void 0:N,imageSrcSet:I,imageSizes:Id,crossOrigin:Jd,integrity:c.integrity,type:c.type,
fetchPriority:c.fetchPriority,referrerPolicy:c.referrerPolicy}),"high"===c.fetchPriority||10>e.highImagePreloads.size?e.highImagePreloads.add(ya):(e.bulkPreloads.add(ya),lc.set(Ia,ya)))}}return tc(a,c,"img");case "base":case "area":case "br":case "col":case "embed":case "hr":case "keygen":case "param":case "source":case "track":case "wbr":return tc(a,c,b);case "annotation-xml":case "color-profile":case "font-face":case "font-face-src":case "font-face-uri":case "font-face-format":case "font-face-name":case "missing-glyph":break;
case "head":if(2>g.insertionMode&&null===e.headChunks){e.headChunks=[];var Kd=xc(e.headChunks,c,"head")}else Kd=xc(a,c,"head");return Kd;case "html":if(0===g.insertionMode&&null===e.htmlChunks){e.htmlChunks=[""];var Ld=xc(e.htmlChunks,c,"html")}else Ld=xc(a,c,"html");return Ld;default:if(-1!==b.indexOf("-")){a.push(P(b));var oc=null,Md=null,Ja;for(Ja in c)if(z.call(c,Ja)){var W=c[Ja];if(null!=W){var Nd=Ja;switch(Ja){case "children":oc=W;break;case "dangerouslySetInnerHTML":Md=W;break;case "style":ac(a,
W);break;case "suppressContentEditableWarning":case "suppressHydrationWarning":case "ref":break;case "className":Nd="class";default:if(nb(Ja)&&"function"!==typeof W&&"symbol"!==typeof W&&!1!==W){if(!0===W)W="";else if("object"===typeof W)continue;a.push(" ",Nd,'="',B(W),'"')}}}}a.push(">");L(a,Md,oc);return oc}}return xc(a,c,b)}var Dc=new Map;function vc(a){var b=Dc.get(a);void 0===b&&(b="</"+a+">",Dc.set(a,b));return b}
function Ec(a,b){b=b.bootstrapChunks;for(var c=0;c<b.length-1;c++)a.push(b[c]);return c<b.length?(c=b[c],b.length=0,a.push(c)):!0}function Fc(a,b,c){a.push('\x3c!--$?--\x3e<template id="');if(null===c)throw Error("An ID must have been assigned before we can complete the boundary.");a.push(b.boundaryPrefix);b=c.toString(16);a.push(b);return a.push('"></template>')}
function Gc(a,b,c,d){switch(c.insertionMode){case 0:case 1:case 2:return a.push('<div hidden id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 3:return a.push('<svg aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 4:return a.push('<math aria-hidden="true" style="display:none" id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 5:return a.push('<table hidden id="'),a.push(b.segmentPrefix),
b=d.toString(16),a.push(b),a.push('">');case 6:return a.push('<table hidden><tbody id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 7:return a.push('<table hidden><tr id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');case 8:return a.push('<table hidden><colgroup id="'),a.push(b.segmentPrefix),b=d.toString(16),a.push(b),a.push('">');default:throw Error("Unknown insertion mode. This is a bug in React.");}}
function Hc(a,b){switch(b.insertionMode){case 0:case 1:case 2:return a.push("</div>");case 3:return a.push("</svg>");case 4:return a.push("</math>");case 5:return a.push("</table>");case 6:return a.push("</tbody></table>");case 7:return a.push("</tr></table>");case 8:return a.push("</colgroup></table>");default:throw Error("Unknown insertion mode. This is a bug in React.");}}var Ic=/[<\u2028\u2029]/g;
function Jc(a){return JSON.stringify(a).replace(Ic,function(b){switch(b){case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSStringsForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Kc=/[&><\u2028\u2029]/g;
function Lc(a){return JSON.stringify(a).replace(Kc,function(b){switch(b){case "&":return"\\u0026";case ">":return"\\u003e";case "<":return"\\u003c";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029";default:throw Error("escapeJSObjectForInstructionScripts encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}})}var Mc=!1,Nc=!0;
function Oc(a){var b=a.rules,c=a.hrefs,d=0;if(c.length){this.push('<style media="not all" data-precedence="');this.push(a.precedence);for(this.push('" data-href="');d<c.length-1;d++)this.push(c[d]),this.push(" ");this.push(c[d]);this.push('">');for(d=0;d<b.length;d++)this.push(b[d]);Nc=this.push("</style>");Mc=!0;b.length=0;c.length=0}}function Pc(a){return 2!==a.state?Mc=!0:!1}function Qc(a,b,c){Mc=!1;Nc=!0;b.styles.forEach(Oc,a);b.stylesheets.forEach(Pc);Mc&&(c.stylesToHoist=!0);return Nc}
function R(a){for(var b=0;b<a.length;b++)this.push(a[b]);a.length=0}var Rc=[];function Sc(a){O(Rc,a.props);for(var b=0;b<Rc.length;b++)this.push(Rc[b]);Rc.length=0;a.state=2}
function Tc(a){var b=0<a.sheets.size;a.sheets.forEach(Sc,this);a.sheets.clear();var c=a.rules,d=a.hrefs;if(!b||d.length){this.push('<style data-precedence="');this.push(a.precedence);a=0;if(d.length){for(this.push('" data-href="');a<d.length-1;a++)this.push(d[a]),this.push(" ");this.push(d[a])}this.push('">');for(a=0;a<c.length;a++)this.push(c[a]);this.push("</style>");c.length=0;d.length=0}}
function Uc(a){if(0===a.state){a.state=1;var b=a.props;O(Rc,{rel:"preload",as:"style",href:a.props.href,crossOrigin:b.crossOrigin,fetchPriority:b.fetchPriority,integrity:b.integrity,media:b.media,hrefLang:b.hrefLang,referrerPolicy:b.referrerPolicy});for(a=0;a<Rc.length;a++)this.push(Rc[a]);Rc.length=0}}function Vc(a){a.sheets.forEach(Uc,this);a.sheets.clear()}
function Wc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=Lc(""+d.props.href),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=Lc(""+d.props.href);a.push(g);e=""+e;a.push(",");e=Lc(e);a.push(e);for(var h in f)if(z.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":case "ref":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!nb(h))break a;g=""+g}e.push(",");k=Lc(k);e.push(k);e.push(",");
g=Lc(g);e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}
function Xc(a,b){a.push("[");var c="[";b.stylesheets.forEach(function(d){if(2!==d.state)if(3===d.state)a.push(c),d=B(JSON.stringify(""+d.props.href)),a.push(d),a.push("]"),c=",[";else{a.push(c);var e=d.props["data-precedence"],f=d.props,g=B(JSON.stringify(""+d.props.href));a.push(g);e=""+e;a.push(",");e=B(JSON.stringify(e));a.push(e);for(var h in f)if(z.call(f,h)&&(g=f[h],null!=g))switch(h){case "href":case "rel":case "precedence":case "data-precedence":break;case "children":case "dangerouslySetInnerHTML":throw Error("link is a self-closing tag and must neither have `children` nor use `dangerouslySetInnerHTML`.");
default:a:{e=a;var k=h.toLowerCase();switch(typeof g){case "function":case "symbol":break a}switch(h){case "innerHTML":case "dangerouslySetInnerHTML":case "suppressContentEditableWarning":case "suppressHydrationWarning":case "style":case "ref":break a;case "className":k="class";g=""+g;break;case "hidden":if(!1===g)break a;g="";break;case "src":case "href":g=""+g;break;default:if(2<h.length&&("o"===h[0]||"O"===h[0])&&("n"===h[1]||"N"===h[1])||!nb(h))break a;g=""+g}e.push(",");k=B(JSON.stringify(k));
e.push(k);e.push(",");g=B(JSON.stringify(g));e.push(g)}}a.push("]");c=",[";d.state=3}});a.push("]")}function Yc(){return{styles:new Set,stylesheets:new Set}}
function Nb(a){var b=T?T:null;if(b){var c=b.resumableState,d=b.renderState;if("string"===typeof a&&a){if(!c.dnsResources.hasOwnProperty(a)){c.dnsResources[a]=null;c=d.headers;var e,f;if(f=c&&0<c.remainingCapacity)f=(e="<"+(""+a).replace(Zc,$c)+">; rel=dns-prefetch",2<=(c.remainingCapacity-=e.length));f?(d.resets.dns[a]=null,c.preconnects&&(c.preconnects+=", "),c.preconnects+=e):(e=[],O(e,{href:a,rel:"dns-prefetch"}),d.preconnects.add(e))}ad(b)}}}
function Ob(a,b){var c=T?T:null;if(c){var d=c.resumableState,e=c.renderState;if("string"===typeof a&&a){var f="use-credentials"===b?"credentials":"string"===typeof b?"anonymous":"default";if(!d.connectResources[f].hasOwnProperty(a)){d.connectResources[f][a]=null;d=e.headers;var g,h;if(h=d&&0<d.remainingCapacity){h="<"+(""+a).replace(Zc,$c)+">; rel=preconnect";if("string"===typeof b){var k=(""+b).replace(bd,cd);h+='; crossorigin="'+k+'"'}h=(g=h,2<=(d.remainingCapacity-=g.length))}h?(e.resets.connect[f][a]=
null,d.preconnects&&(d.preconnects+=", "),d.preconnects+=g):(f=[],O(f,{rel:"preconnect",href:a,crossOrigin:b}),e.preconnects.add(f))}ad(c)}}}
function Pb(a,b,c){var d=T?T:null;if(d){var e=d.resumableState,f=d.renderState;if(b&&a){switch(b){case "image":if(c){var g=c.imageSrcSet;var h=c.imageSizes;var k=c.fetchPriority}var m=g?g+"\n"+(h||""):a;if(e.imageResources.hasOwnProperty(m))return;e.imageResources[m]=D;e=f.headers;var n;e&&0<e.remainingCapacity&&"high"===k&&(n=Cc(a,b,c),2<=(e.remainingCapacity-=n.length))?(f.resets.image[m]=D,e.highImagePreloads&&(e.highImagePreloads+=", "),e.highImagePreloads+=n):(e=[],O(e,u({rel:"preload",href:g?
void 0:a,as:b},c)),"high"===k?f.highImagePreloads.add(e):(f.bulkPreloads.add(e),f.preloads.images.set(m,e)));break;case "style":if(e.styleResources.hasOwnProperty(a))return;g=[];O(g,u({rel:"preload",href:a,as:b},c));e.styleResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?D:[c.crossOrigin,c.integrity];f.preloads.stylesheets.set(a,g);f.bulkPreloads.add(g);break;case "script":if(e.scriptResources.hasOwnProperty(a))return;g=[];f.preloads.scripts.set(a,g);f.bulkPreloads.add(g);
O(g,u({rel:"preload",href:a,as:b},c));e.scriptResources[a]=!c||"string"!==typeof c.crossOrigin&&"string"!==typeof c.integrity?D:[c.crossOrigin,c.integrity];break;default:if(e.unknownResources.hasOwnProperty(b)){if(g=e.unknownResources[b],g.hasOwnProperty(a))return}else g={},e.unknownResources[b]=g;g[a]=D;if((e=f.headers)&&0<e.remainingCapacity&&"font"===b&&(m=Cc(a,b,c),2<=(e.remainingCapacity-=m.length)))f.resets.font[a]=D,e.fontPreloads&&(e.fontPreloads+=", "),e.fontPreloads+=m;else switch(e=[],
a=u({rel:"preload",href:a,as:b},c),O(e,a),b){case "font":f.fontPreloads.add(e);break;default:f.bulkPreloads.add(e)}}ad(d)}}}
function Qb(a,b){var c=T?T:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=b&&"string"===typeof b.as?b.as:"script";switch(f){case "script":if(d.moduleScriptResources.hasOwnProperty(a))return;f=[];d.moduleScriptResources[a]=!b||"string"!==typeof b.crossOrigin&&"string"!==typeof b.integrity?D:[b.crossOrigin,b.integrity];e.preloads.moduleScripts.set(a,f);break;default:if(d.moduleUnknownResources.hasOwnProperty(f)){var g=d.unknownResources[f];if(g.hasOwnProperty(a))return}else g={},d.moduleUnknownResources[f]=
g;f=[];g[a]=D}O(f,u({rel:"modulepreload",href:a},b));e.bulkPreloads.add(f);ad(c)}}}
function Rb(a,b,c){var d=T?T:null;if(d){var e=d.resumableState,f=d.renderState;if(a){b=b||"default";var g=f.styles.get(b),h=e.styleResources.hasOwnProperty(a)?e.styleResources[a]:void 0;null!==h&&(e.styleResources[a]=null,g||(g={precedence:B(b),rules:[],hrefs:[],sheets:new Map},f.styles.set(b,g)),b={state:0,props:u({rel:"stylesheet",href:a,"data-precedence":b},c)},h&&(2===h.length&&Bc(b.props,h),(f=f.preloads.stylesheets.get(a))&&0<f.length?f.length=0:b.state=1),g.sheets.set(a,b),ad(d))}}}
function Sb(a,b){var c=T?T:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.scriptResources.hasOwnProperty(a)?d.scriptResources[a]:void 0;null!==f&&(d.scriptResources[a]=null,b=u({src:a,async:!0},b),f&&(2===f.length&&Bc(b,f),a=e.preloads.scripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),wc(a,b),ad(c))}}}
function Tb(a,b){var c=T?T:null;if(c){var d=c.resumableState,e=c.renderState;if(a){var f=d.moduleScriptResources.hasOwnProperty(a)?d.moduleScriptResources[a]:void 0;null!==f&&(d.moduleScriptResources[a]=null,b=u({src:a,type:"module",async:!0},b),f&&(2===f.length&&Bc(b,f),a=e.preloads.moduleScripts.get(a))&&(a.length=0),a=[],e.scripts.add(a),wc(a,b),ad(c))}}}function Bc(a,b){null==a.crossOrigin&&(a.crossOrigin=b[0]);null==a.integrity&&(a.integrity=b[1])}
function Cc(a,b,c){a=(""+a).replace(Zc,$c);b=(""+b).replace(bd,cd);b="<"+a+'>; rel=preload; as="'+b+'"';for(var d in c)z.call(c,d)&&(a=c[d],"string"===typeof a&&(b+="; "+d.toLowerCase()+'="'+(""+a).replace(bd,cd)+'"'));return b}var Zc=/[<>\r\n]/g;
function $c(a){switch(a){case "<":return"%3C";case ">":return"%3E";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeLinkHrefForHeaderContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}var bd=/["';,\r\n]/g;
function cd(a){switch(a){case '"':return"%22";case "'":return"%27";case ";":return"%3B";case ",":return"%2C";case "\n":return"%0A";case "\r":return"%0D";default:throw Error("escapeStringForLinkHeaderQuotedParamValueContextReplacer encountered a match it does not know how to replace. this means the match regex and the replacement characters are no longer in sync. This is a bug in React");}}function dd(a){this.styles.add(a)}function ed(a){this.stylesheets.add(a)}
function fd(a,b){var c=a.idPrefix,d=[],e=a.bootstrapScriptContent,f=a.bootstrapScripts,g=a.bootstrapModules;void 0!==e&&d.push("<script>",(""+e).replace(Vb,Wb),"\x3c/script>");e=c+"P:";var h=c+"S:";c+="B:";var k=new Set,m=new Set,n=new Set,l=new Map,q=new Set,r=new Set,C=new Set,v={images:new Map,stylesheets:new Map,scripts:new Map,moduleScripts:new Map};if(void 0!==f)for(var x=0;x<f.length;x++){var w=f[x],p,F=void 0,E=void 0,t={rel:"preload",as:"script",fetchPriority:"low",nonce:void 0};"string"===
typeof w?t.href=p=w:(t.href=p=w.src,t.integrity=E="string"===typeof w.integrity?w.integrity:void 0,t.crossOrigin=F="string"===typeof w||null==w.crossOrigin?void 0:"use-credentials"===w.crossOrigin?"use-credentials":"");w=a;var y=p;w.scriptResources[y]=null;w.moduleScriptResources[y]=null;w=[];O(w,t);q.add(w);d.push('<script src="',B(p));"string"===typeof E&&d.push('" integrity="',B(E));"string"===typeof F&&d.push('" crossorigin="',B(F));d.push('" async="">\x3c/script>')}if(void 0!==g)for(f=0;f<g.length;f++)t=
g[f],F=p=void 0,E={rel:"modulepreload",fetchPriority:"low",nonce:void 0},"string"===typeof t?E.href=x=t:(E.href=x=t.src,E.integrity=F="string"===typeof t.integrity?t.integrity:void 0,E.crossOrigin=p="string"===typeof t||null==t.crossOrigin?void 0:"use-credentials"===t.crossOrigin?"use-credentials":""),t=a,w=x,t.scriptResources[w]=null,t.moduleScriptResources[w]=null,t=[],O(t,E),q.add(t),d.push('<script type="module" src="',B(x)),"string"===typeof F&&d.push('" integrity="',B(F)),"string"===typeof p&&
d.push('" crossorigin="',B(p)),d.push('" async="">\x3c/script>');return{placeholderPrefix:e,segmentPrefix:h,boundaryPrefix:c,startInlineScript:"<script>",htmlChunks:null,headChunks:null,externalRuntimeScript:null,bootstrapChunks:d,importMapChunks:[],onHeaders:void 0,headers:null,resets:{font:{},dns:{},connect:{default:{},anonymous:{},credentials:{}},image:{},style:{}},charsetChunks:[],viewportChunks:[],hoistableChunks:[],preconnects:k,fontPreloads:m,highImagePreloads:n,styles:l,bootstrapScripts:q,
scripts:r,bulkPreloads:C,preloads:v,stylesToHoist:!1,generateStaticMarkup:b}}function gd(a,b,c,d){if(c.generateStaticMarkup)return a.push(B(b)),!1;""===b?a=d:(d&&a.push("\x3c!-- --\x3e"),a.push(B(b)),a=!0);return a}var hd=Symbol.for("react.client.reference");
function id(a){if(null==a)return null;if("function"===typeof a)return a.$$typeof===hd?null:a.displayName||a.name||null;if("string"===typeof a)return a;switch(a){case oa:return"Fragment";case ha:return"Portal";case qa:return"Profiler";case pa:return"StrictMode";case Da:return"Suspense";case Ka:return"SuspenseList";case eb:return"Cache"}if("object"===typeof a)switch(a.$$typeof){case za:return(a._context.displayName||"Context")+".Provider";case Ba:return(a.displayName||"Context")+".Consumer";case Ca:var b=
a.render;a=a.displayName;a||(a=b.displayName||b.name||"",a=""!==a?"ForwardRef("+a+")":"ForwardRef");return a;case La:return b=a.displayName||null,null!==b?b:id(a.type)||"Memo";case Ma:b=a._payload;a=a._init;try{return id(a(b))}catch(c){}}return null}var jd={};function kd(a,b){a=a.contextTypes;if(!a)return jd;var c={},d;for(d in a)c[d]=b[d];return c}var ld=null;
function Od(a,b){if(a!==b){a.context._currentValue2=a.parentValue;a=a.parent;var c=b.parent;if(null===a){if(null!==c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");}else{if(null===c)throw Error("The stacks must reach the root at the same time. This is a bug in React.");Od(a,c)}b.context._currentValue2=b.value}}function Pd(a){a.context._currentValue2=a.parentValue;a=a.parent;null!==a&&Pd(a)}
function Qd(a){var b=a.parent;null!==b&&Qd(b);a.context._currentValue2=a.value}function Rd(a,b){a.context._currentValue2=a.parentValue;a=a.parent;if(null===a)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===b.depth?Od(a,b):Rd(a,b)}
function Sd(a,b){var c=b.parent;if(null===c)throw Error("The depth must equal at least at zero before reaching the root. This is a bug in React.");a.depth===c.depth?Od(a,c):Sd(a,c);b.context._currentValue2=b.value}function Td(a){var b=ld;b!==a&&(null===b?Qd(a):null===a?Pd(b):b.depth===a.depth?Od(b,a):b.depth>a.depth?Rd(b,a):Sd(b,a),ld=a)}
var Ud={isMounted:function(){return!1},enqueueSetState:function(a,b){a=a._reactInternals;null!==a.queue&&a.queue.push(b)},enqueueReplaceState:function(a,b){a=a._reactInternals;a.replace=!0;a.queue=[b]},enqueueForceUpdate:function(){}};
function Vd(a,b,c,d){var e=void 0!==a.state?a.state:null;a.updater=Ud;a.props=c;a.state=e;var f={queue:[],replace:!1};a._reactInternals=f;var g=b.contextType;a.context="object"===typeof g&&null!==g?g._currentValue2:d;g=b.getDerivedStateFromProps;"function"===typeof g&&(g=g(c,e),e=null===g||void 0===g?e:u({},e,g),a.state=e);if("function"!==typeof b.getDerivedStateFromProps&&"function"!==typeof a.getSnapshotBeforeUpdate&&("function"===typeof a.UNSAFE_componentWillMount||"function"===typeof a.componentWillMount))if(b=
a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),b!==a.state&&Ud.enqueueReplaceState(a,a.state,null),null!==f.queue&&0<f.queue.length)if(b=f.queue,g=f.replace,f.queue=null,f.replace=!1,g&&1===b.length)a.state=b[0];else{f=g?b[0]:a.state;e=!0;for(g=g?1:0;g<b.length;g++){var h=b[g];h="function"===typeof h?h.call(a,f,c,d):h;null!=h&&(e?(e=!1,f=u({},f,h)):u(f,h))}a.state=f}else f.queue=null}
var Wd={id:1,overflow:""};function Xd(a,b,c){var d=a.id;a=a.overflow;var e=32-Yd(d)-1;d&=~(1<<e);c+=1;var f=32-Yd(b)+e;if(30<f){var g=e-e%5;f=(d&(1<<g)-1).toString(32);d>>=g;e-=g;return{id:1<<32-Yd(b)+e|c<<e|d,overflow:f+a}}return{id:1<<f|c<<e|d,overflow:a}}var Yd=Math.clz32?Math.clz32:Zd,$d=Math.log,ae=Math.LN2;function Zd(a){a>>>=0;return 0===a?32:31-($d(a)/ae|0)|0}var be=Error("Suspense Exception: This is not a real error! It's an implementation detail of `use` to interrupt the current render. You must either rethrow it immediately, or move the `use` call outside of the `try/catch` block. Capturing without rethrowing will lead to unexpected behavior.\n\nTo handle async errors, wrap your component in an error boundary, or call the promise's `.catch` method and pass the result to `use`");
function ce(){}function de(a,b,c){c=a[c];void 0===c?a.push(b):c!==b&&(b.then(ce,ce),b=c);switch(b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;default:if("string"!==typeof b.status)switch(a=b,a.status="pending",a.then(function(d){if("pending"===b.status){var e=b;e.status="fulfilled";e.value=d}},function(d){if("pending"===b.status){var e=b;e.status="rejected";e.reason=d}}),b.status){case "fulfilled":return b.value;case "rejected":throw b.reason;}ee=b;throw be;}}var ee=null;
function fe(){if(null===ee)throw Error("Expected a suspended thenable. This is a bug in React. Please file an issue.");var a=ee;ee=null;return a}function ge(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var he="function"===typeof Object.is?Object.is:ge,ie=null,je=null,ke=null,le=null,me=null,U=null,ne=!1,oe=!1,pe=0,qe=0,re=-1,se=0,te=null,ue=null,ve=0;
function we(){if(null===ie)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component. This could happen for one of the following reasons:\n1. You might have mismatching versions of React and the renderer (such as React DOM)\n2. You might be breaking the Rules of Hooks\n3. You might have more than one copy of React in the same app\nSee https://reactjs.org/link/invalid-hook-call for tips about how to debug and fix this problem.");return ie}
function xe(){if(0<ve)throw Error("Rendered more hooks than during the previous render");return{memoizedState:null,queue:null,next:null}}function ye(){null===U?null===me?(ne=!1,me=U=xe()):(ne=!0,U=me):null===U.next?(ne=!1,U=U.next=xe()):(ne=!0,U=U.next);return U}function ze(){var a=te;te=null;return a}function Ae(){le=ke=je=ie=null;oe=!1;me=null;ve=0;U=ue=null}function Be(a,b){return"function"===typeof b?b(a):b}
function Ce(a,b,c){ie=we();U=ye();if(ne){var d=U.queue;b=d.dispatch;if(null!==ue&&(c=ue.get(d),void 0!==c)){ue.delete(d);d=U.memoizedState;do d=a(d,c.action),c=c.next;while(null!==c);U.memoizedState=d;return[d,b]}return[U.memoizedState,b]}a=a===Be?"function"===typeof b?b():b:void 0!==c?c(b):b;U.memoizedState=a;a=U.queue={last:null,dispatch:null};a=a.dispatch=De.bind(null,ie,a);return[U.memoizedState,a]}
function Ee(a,b){ie=we();U=ye();b=void 0===b?null:b;if(null!==U){var c=U.memoizedState;if(null!==c&&null!==b){var d=c[1];a:if(null===d)d=!1;else{for(var e=0;e<d.length&&e<b.length;e++)if(!he(b[e],d[e])){d=!1;break a}d=!0}if(d)return c[0]}}a=a();U.memoizedState=[a,b];return a}
function De(a,b,c){if(25<=ve)throw Error("Too many re-renders. React limits the number of renders to prevent an infinite loop.");if(a===ie)if(oe=!0,a={action:c,next:null},null===ue&&(ue=new Map),c=ue.get(b),void 0===c)ue.set(b,a);else{for(b=c;null!==b.next;)b=b.next;b.next=a}}function Fe(){throw Error("A function wrapped in useEffectEvent can't be called during rendering.");}function Ge(){throw Error("startTransition cannot be called during server rendering.");}
function He(){throw Error("Cannot update optimistic state while rendering.");}function Ie(a){var b=se;se+=1;null===te&&(te=[]);return de(te,a,b)}function Je(){throw Error("Cache cannot be refreshed during server rendering.");}function Ke(){}
var Me={readContext:function(a){return a._currentValue2},use:function(a){if(null!==a&&"object"===typeof a){if("function"===typeof a.then)return Ie(a);if(a.$$typeof===Ba)return a._currentValue2}throw Error("An unsupported type was passed to use(): "+String(a));},useContext:function(a){we();return a._currentValue2},useMemo:Ee,useReducer:Ce,useRef:function(a){ie=we();U=ye();var b=U.memoizedState;return null===b?(a={current:a},U.memoizedState=a):b},useState:function(a){return Ce(Be,a)},useInsertionEffect:Ke,
useLayoutEffect:Ke,useCallback:function(a,b){return Ee(function(){return a},b)},useImperativeHandle:Ke,useEffect:Ke,useDebugValue:Ke,useDeferredValue:function(a,b){we();return void 0!==b?b:a},useTransition:function(){we();return[!1,Ge]},useId:function(){var a=je.treeContext;var b=a.overflow;a=a.id;a=(a&~(1<<32-Yd(a)-1)).toString(32)+b;var c=Le;if(null===c)throw Error("Invalid hook call. Hooks can only be called inside of the body of a function component.");b=pe++;a=":"+c.idPrefix+"R"+a;0<b&&(a+="H"+
b.toString(32));return a+":"},useSyncExternalStore:function(a,b,c){if(void 0===c)throw Error("Missing getServerSnapshot, which is required for server-rendered content. Will revert to client rendering.");return c()},useCacheRefresh:function(){return Je},useEffectEvent:function(){return Fe},useMemoCache:function(a){for(var b=Array(a),c=0;c<a;c++)b[c]=fb;return b},useHostTransitionStatus:function(){we();return Lb},useOptimistic:function(a){we();return[a,He]},useFormState:function(a,b,c){we();var d=qe++,
e=ke;if("function"===typeof a.$$FORM_ACTION){var f=null,g=le;e=e.formState;var h=a.$$IS_SIGNATURE_EQUAL;if(null!==e&&"function"===typeof h){var k=e[1];h.call(a,e[2],e[3])&&(f=void 0!==c?"p"+c:"k"+jb(JSON.stringify([g,null,d]),0),k===f&&(re=d,b=e[0]))}var m=a.bind(null,b);a=function(l){m(l)};"function"===typeof m.$$FORM_ACTION&&(a.$$FORM_ACTION=function(l){l=m.$$FORM_ACTION(l);void 0!==c&&(c+="",l.action=c);var q=l.data;q&&(null===f&&(f=void 0!==c?"p"+c:"k"+jb(JSON.stringify([g,null,d]),0)),q.append("$ACTION_KEY",
f));return l});return[b,a]}var n=a.bind(null,b);return[b,function(l){n(l)}]}},Le=null,Ne={getCacheSignal:function(){throw Error("Not implemented.");},getCacheForType:function(){throw Error("Not implemented.");}},Oe;function Pe(a){if(void 0===Oe)try{throw Error();}catch(c){var b=c.stack.trim().match(/\n( *(at )?)/);Oe=b&&b[1]||""}return"\n"+Oe+a}var Qe=!1;
function Re(a,b){if(!a||Qe)return"";Qe=!0;var c=Error.prepareStackTrace;Error.prepareStackTrace=void 0;var d={DetermineComponentFrameRoot:function(){try{if(b){var l=function(){throw Error();};Object.defineProperty(l.prototype,"props",{set:function(){throw Error();}});if("object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(l,[])}catch(r){var q=r}Reflect.construct(a,[],l)}else{try{l.call()}catch(r){q=r}a.call(l.prototype)}}else{try{throw Error();}catch(r){q=r}(l=a())&&"function"===typeof l.catch&&
l.catch(function(){})}}catch(r){if(r&&q&&"string"===typeof r.stack)return[r.stack,q.stack]}return[null,null]}};d.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var e=Object.getOwnPropertyDescriptor(d.DetermineComponentFrameRoot,"name");e&&e.configurable&&Object.defineProperty(d.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});try{var f=d.DetermineComponentFrameRoot(),g=f[0],h=f[1];if(g&&h){var k=g.split("\n"),m=h.split("\n");for(e=d=0;d<k.length&&!k[d].includes("DetermineComponentFrameRoot");)d++;
for(;e<m.length&&!m[e].includes("DetermineComponentFrameRoot");)e++;if(d===k.length||e===m.length)for(d=k.length-1,e=m.length-1;1<=d&&0<=e&&k[d]!==m[e];)e--;for(;1<=d&&0<=e;d--,e--)if(k[d]!==m[e]){if(1!==d||1!==e){do if(d--,e--,0>e||k[d]!==m[e]){var n="\n"+k[d].replace(" at new "," at ");a.displayName&&n.includes("<anonymous>")&&(n=n.replace("<anonymous>",a.displayName));return n}while(1<=d&&0<=e)}break}}}finally{Qe=!1,Error.prepareStackTrace=c}return(c=a?a.displayName||a.name:"")?Pe(c):""}
var Se=Kb.ReactCurrentDispatcher,Te=Kb.ReactCurrentCache;function Ue(a){console.error(a);return null}function Ve(){}
function We(a,b,c,d,e,f,g,h,k,m,n,l){Mb.current=Ub;var q=[],r=new Set;b={destination:null,flushScheduled:!1,resumableState:b,renderState:c,rootFormatContext:d,progressiveChunkSize:void 0===e?12800:e,status:0,fatalError:null,nextSegmentId:0,allPendingTasks:0,pendingRootTasks:0,completedRootSegment:null,abortableTasks:r,pingedTasks:q,clientRenderedBoundaries:[],completedBoundaries:[],partialBoundaries:[],trackedPostpones:null,onError:void 0===f?Ue:f,onPostpone:void 0===n?Ve:n,onAllReady:void 0===g?
Ve:g,onShellReady:void 0===h?Ve:h,onShellError:void 0===k?Ve:k,onFatalError:void 0===m?Ve:m,formState:void 0===l?null:l};c=Xe(b,0,null,d,!1,!1);c.parentFlushed=!0;a=Ye(b,null,a,-1,null,c,null,r,null,d,jd,null,Wd,null,!1);q.push(a);return b}var T=null;function Ze(a,b){a.pingedTasks.push(b);1===a.pingedTasks.length&&(a.flushScheduled=null!==a.destination,$e(a))}
function af(a,b){return{status:0,rootSegmentID:-1,parentFlushed:!1,pendingTasks:0,completedSegments:[],byteSize:0,fallbackAbortableTasks:b,errorDigest:null,contentState:Yc(),fallbackState:Yc(),trackedContentKeyPath:null,trackedFallbackNode:null}}
function Ye(a,b,c,d,e,f,g,h,k,m,n,l,q,r,C){a.allPendingTasks++;null===e?a.pendingRootTasks++:e.pendingTasks++;var v={replay:null,node:c,childIndex:d,ping:function(){return Ze(a,v)},blockedBoundary:e,blockedSegment:f,hoistableState:g,abortSet:h,keyPath:k,formatContext:m,legacyContext:n,context:l,treeContext:q,componentStack:r,thenableState:b,isFallback:C};h.add(v);return v}
function bf(a,b,c,d,e,f,g,h,k,m,n,l,q,r,C){a.allPendingTasks++;null===f?a.pendingRootTasks++:f.pendingTasks++;c.pendingTasks++;var v={replay:c,node:d,childIndex:e,ping:function(){return Ze(a,v)},blockedBoundary:f,blockedSegment:null,hoistableState:g,abortSet:h,keyPath:k,formatContext:m,legacyContext:n,context:l,treeContext:q,componentStack:r,thenableState:b,isFallback:C};h.add(v);return v}
function Xe(a,b,c,d,e,f){return{status:0,id:-1,index:b,parentFlushed:!1,chunks:[],children:[],parentFormatContext:d,boundary:c,lastPushedText:e,textEmbedded:f}}function cf(a,b){return{tag:0,parent:a.componentStack,type:b}}
function df(a,b){if(b&&null!==a.trackedPostpones){try{a="";do{switch(b.tag){case 0:a+=Pe(b.type,null);break;case 1:a+=Re(b.type,!1);break;case 2:a+=Re(b.type,!0)}b=b.parent}while(b);var c=a}catch(d){c="\nError generating stack: "+d.message+"\n"+d.stack}c={componentStack:c}}else c={};return c}function V(a,b,c){a=a.onError(b,c);if(null==a||"string"===typeof a)return a}
function ef(a,b){var c=a.onShellError;c(b);c=a.onFatalError;c(b);null!==a.destination?(a.status=2,a.destination.destroy(b)):(a.status=1,a.fatalError=b)}function ff(a,b,c,d,e,f){var g=b.thenableState;b.thenableState=null;ie={};je=b;ke=a;le=c;qe=pe=0;re=-1;se=0;te=g;for(a=d(e,f);oe;)oe=!1,qe=pe=0,re=-1,se=0,ve+=1,U=null,a=d(e,f);Ae();return a}
function gf(a,b,c,d,e){var f=d.render(),g=e.childContextTypes;if(null!==g&&void 0!==g){c=b.legacyContext;if("function"!==typeof d.getChildContext)e=c;else{d=d.getChildContext();for(var h in d)if(!(h in g))throw Error((id(e)||"Unknown")+'.getChildContext(): key "'+h+'" is not defined in childContextTypes.');e=u({},c,d)}b.legacyContext=e;X(a,b,f,-1);b.legacyContext=c}else e=b.keyPath,b.keyPath=c,X(a,b,f,-1),b.keyPath=e}
function hf(a,b,c,d,e,f,g){var h=!1;if(0!==f&&null!==a.formState){var k=b.blockedSegment;if(null!==k){h=!0;k=k.chunks;for(var m=0;m<f;m++)m===g?k.push("\x3c!--F!--\x3e"):k.push("\x3c!--F--\x3e")}}f=b.keyPath;b.keyPath=c;e?(c=b.treeContext,b.treeContext=Xd(c,1,0),lf(a,b,d,-1),b.treeContext=c):h?lf(a,b,d,-1):X(a,b,d,-1);b.keyPath=f}function mf(a,b){if(a&&a.defaultProps){b=u({},b);a=a.defaultProps;for(var c in a)void 0===b[c]&&(b[c]=a[c]);return b}return b}
function nf(a,b,c,d,e,f){if("function"===typeof d)if(d.prototype&&d.prototype.isReactComponent){f=b.componentStack;b.componentStack={tag:2,parent:b.componentStack,type:d};var g=kd(d,b.legacyContext);var h=d.contextType;h=new d(e,"object"===typeof h&&null!==h?h._currentValue2:g);Vd(h,d,e,g);gf(a,b,c,h,d);b.componentStack=f}else{f=kd(d,b.legacyContext);g=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:d};h=ff(a,b,c,d,e,f);var k=0!==pe,m=qe,n=re;"object"===typeof h&&null!==h&&"function"===
typeof h.render&&void 0===h.$$typeof?(Vd(h,d,e,f),gf(a,b,c,h,d)):hf(a,b,c,h,k,m,n);b.componentStack=g}else if("string"===typeof d){f=b.componentStack;b.componentStack=cf(b,d);g=b.blockedSegment;if(null===g)g=e.children,h=b.formatContext,k=b.keyPath,b.formatContext=Zb(h,d,e),b.keyPath=c,lf(a,b,g,-1),b.formatContext=h,b.keyPath=k;else{k=Ac(g.chunks,d,e,a.resumableState,a.renderState,b.hoistableState,b.formatContext,g.lastPushedText,b.isFallback);g.lastPushedText=!1;h=b.formatContext;m=b.keyPath;b.formatContext=
Zb(h,d,e);b.keyPath=c;lf(a,b,k,-1);b.formatContext=h;b.keyPath=m;a:{c=g.chunks;a=a.resumableState;switch(d){case "title":case "style":case "script":case "area":case "base":case "br":case "col":case "embed":case "hr":case "img":case "input":case "keygen":case "link":case "meta":case "param":case "source":case "track":case "wbr":break a;case "body":if(1>=h.insertionMode){a.hasBody=!0;break a}break;case "html":if(0===h.insertionMode){a.hasHtml=!0;break a}}c.push(vc(d))}g.lastPushedText=!1}b.componentStack=
f}else{switch(d){case Ya:case Oa:case pa:case qa:case oa:d=b.keyPath;b.keyPath=c;X(a,b,e.children,-1);b.keyPath=d;return;case Pa:"hidden"!==e.mode&&(d=b.keyPath,b.keyPath=c,X(a,b,e.children,-1),b.keyPath=d);return;case Ka:d=b.componentStack;b.componentStack=cf(b,"SuspenseList");f=b.keyPath;b.keyPath=c;X(a,b,e.children,-1);b.keyPath=f;b.componentStack=d;return;case Na:throw Error("ReactDOMServer does not yet support scope components.");case Da:a:if(null!==b.replay){d=b.keyPath;b.keyPath=c;c=e.children;
try{lf(a,b,c,-1)}finally{b.keyPath=d}}else{var l=b.componentStack;d=b.componentStack=cf(b,"Suspense");var q=b.keyPath;f=b.blockedBoundary;var r=b.hoistableState,C=b.blockedSegment;g=e.fallback;var v=e.children;e=new Set;m=af(a,e);null!==a.trackedPostpones&&(m.trackedContentKeyPath=c);n=Xe(a,C.chunks.length,m,b.formatContext,!1,!1);C.children.push(n);C.lastPushedText=!1;var x=Xe(a,0,null,b.formatContext,!1,!1);x.parentFlushed=!0;b.blockedBoundary=m;b.hoistableState=m.contentState;b.blockedSegment=
x;b.keyPath=c;try{if(lf(a,b,v,-1),a.renderState.generateStaticMarkup||x.lastPushedText&&x.textEmbedded&&x.chunks.push("\x3c!-- --\x3e"),x.status=1,of(m,x),0===m.pendingTasks&&0===m.status){m.status=1;b.componentStack=l;break a}}catch(w){x.status=4,m.status=4,h=df(a,b.componentStack),"object"===typeof w&&null!==w&&w.$$typeof===gb?(a.onPostpone(w.message,h),k="POSTPONE"):k=V(a,w,h),m.errorDigest=k,pf(a,m)}finally{b.blockedBoundary=f,b.hoistableState=r,b.blockedSegment=C,b.keyPath=q,b.componentStack=
l}h=[c[0],"Suspense Fallback",c[2]];k=a.trackedPostpones;null!==k&&(l=[h[1],h[2],[],null],k.workingMap.set(h,l),5===m.status?k.workingMap.get(c)[4]=l:m.trackedFallbackNode=l);b=Ye(a,null,g,-1,f,n,m.fallbackState,e,h,b.formatContext,b.legacyContext,b.context,b.treeContext,d,!0);a.pingedTasks.push(b)}return}if("object"===typeof d&&null!==d)switch(d.$$typeof){case Ca:h=b.componentStack;b.componentStack={tag:1,parent:b.componentStack,type:d.render};if("ref"in e)for(g in k={},e)"ref"!==g&&(k[g]=e[g]);
else k=e;e=ff(a,b,c,d.render,k,f);hf(a,b,c,e,0!==pe,qe,re);b.componentStack=h;return;case La:d=d.type;e=mf(d,e);nf(a,b,c,d,e,f);return;case za:g=e.children;f=b.keyPath;d=d._context;e=e.value;h=d._currentValue2;d._currentValue2=e;k=ld;ld=e={parent:k,depth:null===k?0:k.depth+1,context:d,parentValue:h,value:e};b.context=e;b.keyPath=c;X(a,b,g,-1);a=ld;if(null===a)throw Error("Tried to pop a Context at the root of the app. This is a bug in React.");a.context._currentValue2=a.parentValue;a=ld=a.parent;
b.context=a;b.keyPath=f;return;case Ba:e=e.children;e=e(d._currentValue2);d=b.keyPath;b.keyPath=c;X(a,b,e,-1);b.keyPath=d;return;case Aa:case Ma:f=b.componentStack;b.componentStack=cf(b,"Lazy");g=d._init;d=g(d._payload);e=mf(d,e);nf(a,b,c,d,e,void 0);b.componentStack=f;return}throw Error("Element type is invalid: expected a string (for built-in components) or a class/function (for composite components) but got: "+((null==d?d:typeof d)+"."));}}
function qf(a,b,c,d,e){var f=b.replay,g=b.blockedBoundary,h=Xe(a,0,null,b.formatContext,!1,!1);h.id=c;h.parentFlushed=!0;try{b.replay=null,b.blockedSegment=h,lf(a,b,d,e),h.status=1,null===g?a.completedRootSegment=h:(of(g,h),g.parentFlushed&&a.partialBoundaries.push(g))}finally{b.replay=f,b.blockedSegment=null}}
function X(a,b,c,d){if(null!==b.replay&&"number"===typeof b.replay.slots)qf(a,b,b.replay.slots,c,d);else if(b.node=c,b.childIndex=d,null!==c){if("object"===typeof c){switch(c.$$typeof){case fa:var e=c.type,f=c.key,g=c.props;c=g.ref;var h=void 0!==c?c:null;var k=id(e),m=null==f?-1===d?0:d:f;f=[b.keyPath,k,m];if(null!==b.replay)a:{var n=b.replay;d=n.nodes;for(c=0;c<d.length;c++){var l=d[c];if(m===l[1]){if(4===l.length){if(null!==k&&k!==l[0])throw Error("Expected the resume to render <"+l[0]+"> in this slot but instead it rendered <"+
k+">. The tree doesn't match so React will fallback to client rendering.");var q=l[2];k=l[3];m=b.node;b.replay={nodes:q,slots:k,pendingTasks:1};try{nf(a,b,f,e,g,h);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(y){if("object"===typeof y&&null!==y&&(y===be||"function"===typeof y.then))throw b.node===m&&(b.replay=n),y;
b.replay.pendingTasks--;g=df(a,b.componentStack);rf(a,b.blockedBoundary,y,g,q,k)}b.replay=n}else{if(e!==Da)throw Error("Expected the resume to render <Suspense> in this slot but instead it rendered <"+(id(e)||"Unknown")+">. The tree doesn't match so React will fallback to client rendering.");b:{e=void 0;h=l[5];n=l[2];k=l[3];m=null===l[4]?[]:l[4][2];l=null===l[4]?null:l[4][3];var r=b.componentStack,C=b.componentStack=cf(b,"Suspense"),v=b.keyPath,x=b.replay,w=b.blockedBoundary,p=b.hoistableState,F=
g.children;g=g.fallback;var E=new Set,t=af(a,E);t.parentFlushed=!0;t.rootSegmentID=h;b.blockedBoundary=t;b.hoistableState=t.contentState;b.replay={nodes:n,slots:k,pendingTasks:1};try{lf(a,b,F,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--;if(0===t.pendingTasks&&0===t.status){t.status=1;a.completedBoundaries.push(t);break b}}catch(y){t.status=
4,q=df(a,b.componentStack),"object"===typeof y&&null!==y&&y.$$typeof===gb?(a.onPostpone(y.message,q),e="POSTPONE"):e=V(a,y,q),t.errorDigest=e,b.replay.pendingTasks--,a.clientRenderedBoundaries.push(t)}finally{b.blockedBoundary=w,b.hoistableState=p,b.replay=x,b.keyPath=v,b.componentStack=r}q=bf(a,null,{nodes:m,slots:l,pendingTasks:0},g,-1,w,t.fallbackState,E,[f[0],"Suspense Fallback",f[2]],b.formatContext,b.legacyContext,b.context,b.treeContext,C,!0);a.pingedTasks.push(q)}}d.splice(c,1);break a}}}else nf(a,
b,f,e,g,h);return;case ha:throw Error("Portals are not currently supported by the server renderer. Render them conditionally so that they only appear on the client render.");case Ma:q=b.componentStack;b.componentStack=cf(b,"Lazy");g=c._init;c=g(c._payload);b.componentStack=q;X(a,b,c,d);return}if(ib(c)){sf(a,b,c,d);return}null===c||"object"!==typeof c?q=null:(q=hb&&c[hb]||c["@@iterator"],q="function"===typeof q?q:null);if(q&&(q=q.call(c))){c=q.next();if(!c.done){g=[];do g.push(c.value),c=q.next();
while(!c.done);sf(a,b,g,d)}return}if("function"===typeof c.then)return b.thenableState=null,X(a,b,Ie(c),d);if(c.$$typeof===Ba)return X(a,b,c._currentValue2,d);d=Object.prototype.toString.call(c);throw Error("Objects are not valid as a React child (found: "+("[object Object]"===d?"object with keys {"+Object.keys(c).join(", ")+"}":d)+"). If you meant to render a collection of children, use an array instead.");}"string"===typeof c?(d=b.blockedSegment,null!==d&&(d.lastPushedText=gd(d.chunks,c,a.renderState,
d.lastPushedText))):"number"===typeof c&&(d=b.blockedSegment,null!==d&&(d.lastPushedText=gd(d.chunks,""+c,a.renderState,d.lastPushedText)))}}
function sf(a,b,c,d){var e=b.keyPath;if(-1!==d&&(b.keyPath=[b.keyPath,"Fragment",d],null!==b.replay)){for(var f=b.replay,g=f.nodes,h=0;h<g.length;h++){var k=g[h];if(k[1]===d){d=k[2];k=k[3];b.replay={nodes:d,slots:k,pendingTasks:1};try{sf(a,b,c,-1);if(1===b.replay.pendingTasks&&0<b.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");b.replay.pendingTasks--}catch(n){if("object"===typeof n&&
null!==n&&(n===be||"function"===typeof n.then))throw n;b.replay.pendingTasks--;c=df(a,b.componentStack);rf(a,b.blockedBoundary,n,c,d,k)}b.replay=f;g.splice(h,1);break}}b.keyPath=e;return}f=b.treeContext;g=c.length;if(null!==b.replay&&(h=b.replay.slots,null!==h&&"object"===typeof h)){for(d=0;d<g;d++){k=c[d];b.treeContext=Xd(f,g,d);var m=h[d];"number"===typeof m?(qf(a,b,m,k,d),delete h[d]):lf(a,b,k,d)}b.treeContext=f;b.keyPath=e;return}for(h=0;h<g;h++)d=c[h],b.treeContext=Xd(f,g,h),lf(a,b,d,h);b.treeContext=
f;b.keyPath=e}
function tf(a,b,c,d){d.status=5;var e=c.keyPath,f=c.blockedBoundary;if(null===f)d.id=a.nextSegmentId++,b.rootSlots=d.id,null!==a.completedRootSegment&&(a.completedRootSegment.status=5);else{if(null!==f&&0===f.status){f.status=5;f.rootSegmentID=a.nextSegmentId++;var g=f.trackedContentKeyPath;if(null===g)throw Error("It should not be possible to postpone at the root. This is a bug in React.");var h=f.trackedFallbackNode,k=[];if(g===e&&-1===c.childIndex){-1===d.id&&(d.id=d.parentFlushed?f.rootSegmentID:
a.nextSegmentId++);d=[g[1],g[2],k,d.id,h,f.rootSegmentID];b.workingMap.set(g,d);uf(d,g[0],b);return}var m=b.workingMap.get(g);void 0===m?(m=[g[1],g[2],k,null,h,f.rootSegmentID],b.workingMap.set(g,m),uf(m,g[0],b)):(g=m,g[4]=h,g[5]=f.rootSegmentID)}-1===d.id&&(d.id=d.parentFlushed&&null!==f?f.rootSegmentID:a.nextSegmentId++);if(-1===c.childIndex)null===e?b.rootSlots=d.id:(c=b.workingMap.get(e),void 0===c?(c=[e[1],e[2],[],d.id],uf(c,e[0],b)):c[3]=d.id);else{if(null===e)if(a=b.rootSlots,null===a)a=b.rootSlots=
{};else{if("number"===typeof a)throw Error("It should not be possible to postpone both at the root of an element as well as a slot below. This is a bug in React.");}else if(f=b.workingMap,g=f.get(e),void 0===g)a={},g=[e[1],e[2],[],a],f.set(e,g),uf(g,e[0],b);else if(a=g[3],null===a)a=g[3]={};else if("number"===typeof a)throw Error("It should not be possible to postpone both at the root of an element as well as a slot below. This is a bug in React.");a[c.childIndex]=d.id}}}
function pf(a,b){a=a.trackedPostpones;null!==a&&(b=b.trackedContentKeyPath,null!==b&&(b=a.workingMap.get(b),void 0!==b&&(b.length=4,b[2]=[],b[3]=null)))}
function lf(a,b,c,d){var e=b.formatContext,f=b.legacyContext,g=b.context,h=b.keyPath,k=b.treeContext,m=b.componentStack,n=b.blockedSegment;if(null===n)try{return X(a,b,c,d)}catch(r){if(Ae(),d=r===be?fe():r,"object"===typeof d&&null!==d&&"function"===typeof d.then){c=d;d=ze();a=bf(a,d,b.replay,b.node,b.childIndex,b.blockedBoundary,b.hoistableState,b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null,b.isFallback).ping;c.then(a,
a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=m;Td(g);return}}else{var l=n.children.length,q=n.chunks.length;try{return X(a,b,c,d)}catch(r){if(Ae(),n.children.length=l,n.chunks.length=q,d=r===be?fe():r,"object"===typeof d&&null!==d){if("function"===typeof d.then){c=d;d=ze();n=b.blockedSegment;l=Xe(a,n.chunks.length,null,b.formatContext,n.lastPushedText,!0);n.children.push(l);n.lastPushedText=!1;a=Ye(a,d,b.node,b.childIndex,b.blockedBoundary,l,b.hoistableState,
b.abortSet,b.keyPath,b.formatContext,b.legacyContext,b.context,b.treeContext,null!==b.componentStack?b.componentStack.parent:null,b.isFallback).ping;c.then(a,a);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=m;Td(g);return}if(d.$$typeof===gb&&null!==a.trackedPostpones&&null!==b.blockedBoundary){c=a.trackedPostpones;n=df(a,b.componentStack);a.onPostpone(d.message,n);d=b.blockedSegment;n=Xe(a,d.chunks.length,null,b.formatContext,d.lastPushedText,!0);d.children.push(n);
d.lastPushedText=!1;tf(a,c,b,n);b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;b.componentStack=m;Td(g);return}}}}b.formatContext=e;b.legacyContext=f;b.context=g;b.keyPath=h;b.treeContext=k;Td(g);throw d;}function rf(a,b,c,d,e,f){"object"===typeof c&&null!==c&&c.$$typeof===gb?(a.onPostpone(c.message,d),d="POSTPONE"):d=V(a,c,d);vf(a,b,e,f,c,d)}function wf(a){var b=a.blockedBoundary;a=a.blockedSegment;null!==a&&(a.status=3,xf(this,b,a))}
function vf(a,b,c,d,e,f){for(var g=0;g<c.length;g++){var h=c[g];if(4===h.length)vf(a,b,h[2],h[3],e,f);else{h=h[5];var k=a,m=f,n=af(k,new Set);n.parentFlushed=!0;n.rootSegmentID=h;n.status=4;n.errorDigest=m;n.parentFlushed&&k.clientRenderedBoundaries.push(n)}}c.length=0;if(null!==d){if(null===b)throw Error("We should not have any resumable nodes in the shell. This is a bug in React.");4!==b.status&&(b.status=4,b.errorDigest=f,b.parentFlushed&&a.clientRenderedBoundaries.push(b));if("object"===typeof d)for(var l in d)delete d[l]}}
function yf(a,b,c){var d=a.blockedBoundary,e=a.blockedSegment;null!==e&&(e.status=3);if(null===d){if(d={},1!==b.status&&2!==b.status){a=a.replay;if(null===a){"object"===typeof c&&null!==c&&c.$$typeof===gb?(a=Error("The render was aborted with postpone when the shell is incomplete. Reason: "+c.message),V(b,a,d),ef(b,a)):(V(b,c,d),ef(b,c));return}a.pendingTasks--;0===a.pendingTasks&&0<a.nodes.length&&("object"===typeof c&&null!==c&&c.$$typeof===gb?(b.onPostpone(c.message,d),d="POSTPONE"):d=V(b,c,d),
vf(b,null,a.nodes,a.slots,c,d));b.pendingRootTasks--;0===b.pendingRootTasks&&zf(b)}}else d.pendingTasks--,4!==d.status&&(d.status=4,a=df(b,a.componentStack),"object"===typeof c&&null!==c&&c.$$typeof===gb?(b.onPostpone(c.message,a),a="POSTPONE"):a=V(b,c,a),d.errorDigest=a,pf(b,d),d.parentFlushed&&b.clientRenderedBoundaries.push(d)),d.fallbackAbortableTasks.forEach(function(f){return yf(f,b,c)}),d.fallbackAbortableTasks.clear();b.allPendingTasks--;0===b.allPendingTasks&&Af(b)}
function Bf(a,b){try{var c=a.renderState,d=c.onHeaders;if(d){var e=c.headers;if(e){c.headers=null;var f=e.preconnects;e.fontPreloads&&(f&&(f+=", "),f+=e.fontPreloads);e.highImagePreloads&&(f&&(f+=", "),f+=e.highImagePreloads);if(!b){var g=c.styles.values(),h=g.next();b:for(;0<e.remainingCapacity&&!h.done;h=g.next())for(var k=h.value.sheets.values(),m=k.next();0<e.remainingCapacity&&!m.done;m=k.next()){var n=m.value,l=n.props,q=l.href,r=n.props,C=Cc(r.href,"style",{crossOrigin:r.crossOrigin,integrity:r.integrity,
nonce:r.nonce,type:r.type,fetchPriority:r.fetchPriority,referrerPolicy:r.referrerPolicy,media:r.media});if(2<=(e.remainingCapacity-=C.length))c.resets.style[q]=D,f&&(f+=", "),f+=C,c.resets.style[q]="string"===typeof l.crossOrigin||"string"===typeof l.integrity?[l.crossOrigin,l.integrity]:D;else break b}}f?d({Link:f}):d({})}}}catch(v){V(a,v,{})}}function zf(a){null===a.trackedPostpones&&Bf(a,!0);a.onShellError=Ve;a=a.onShellReady;a()}
function Af(a){Bf(a,null===a.trackedPostpones?!0:null===a.completedRootSegment||5!==a.completedRootSegment.status);a=a.onAllReady;a()}function of(a,b){if(0===b.chunks.length&&1===b.children.length&&null===b.children[0].boundary&&-1===b.children[0].id){var c=b.children[0];c.id=b.id;c.parentFlushed=!0;1===c.status&&of(a,c)}else a.completedSegments.push(b)}
function xf(a,b,c){if(null===b){if(null!==c&&c.parentFlushed){if(null!==a.completedRootSegment)throw Error("There can only be one root segment. This is a bug in React.");a.completedRootSegment=c}a.pendingRootTasks--;0===a.pendingRootTasks&&zf(a)}else b.pendingTasks--,4!==b.status&&(0===b.pendingTasks?(0===b.status&&(b.status=1),null!==c&&c.parentFlushed&&1===c.status&&of(b,c),b.parentFlushed&&a.completedBoundaries.push(b),1===b.status&&(b.fallbackAbortableTasks.forEach(wf,a),b.fallbackAbortableTasks.clear())):
null!==c&&c.parentFlushed&&1===c.status&&(of(b,c),1===b.completedSegments.length&&b.parentFlushed&&a.partialBoundaries.push(b)));a.allPendingTasks--;0===a.allPendingTasks&&Af(a)}
function $e(a){if(2!==a.status){var b=ld,c=Se.current;Se.current=Me;var d=Te.current;Te.current=Ne;var e=T;T=a;var f=Le;Le=a.resumableState;try{var g=a.pingedTasks,h;for(h=0;h<g.length;h++){var k=g[h],m=a,n=k.blockedSegment;if(null===n){var l=m;if(0!==k.replay.pendingTasks){Td(k.context);try{X(l,k,k.node,k.childIndex);if(1===k.replay.pendingTasks&&0<k.replay.nodes.length)throw Error("Couldn't find all resumable slots by key/index during replaying. The tree doesn't match so React will fallback to client rendering.");
k.replay.pendingTasks--;k.abortSet.delete(k);xf(l,k.blockedBoundary,null)}catch(G){Ae();var q=G===be?fe():G;if("object"===typeof q&&null!==q&&"function"===typeof q.then){var r=k.ping;q.then(r,r);k.thenableState=ze()}else{k.replay.pendingTasks--;k.abortSet.delete(k);var C=df(l,k.componentStack);rf(l,k.blockedBoundary,q,C,k.replay.nodes,k.replay.slots);l.pendingRootTasks--;0===l.pendingRootTasks&&zf(l);l.allPendingTasks--;0===l.allPendingTasks&&Af(l)}}finally{}}}else a:{l=void 0;var v=n;if(0===v.status){Td(k.context);
var x=v.children.length,w=v.chunks.length;try{X(m,k,k.node,k.childIndex),m.renderState.generateStaticMarkup||v.lastPushedText&&v.textEmbedded&&v.chunks.push("\x3c!-- --\x3e"),k.abortSet.delete(k),v.status=1,xf(m,k.blockedBoundary,v)}catch(G){Ae();v.children.length=x;v.chunks.length=w;var p=G===be?fe():G;if("object"===typeof p&&null!==p){if("function"===typeof p.then){var F=k.ping;p.then(F,F);k.thenableState=ze();break a}if(null!==m.trackedPostpones&&p.$$typeof===gb){var E=m.trackedPostpones;k.abortSet.delete(k);
var t=df(m,k.componentStack);m.onPostpone(p.message,t);tf(m,E,k,v);xf(m,k.blockedBoundary,v);break a}}var y=df(m,k.componentStack);k.abortSet.delete(k);v.status=4;var A=k.blockedBoundary;"object"===typeof p&&null!==p&&p.$$typeof===gb?(m.onPostpone(p.message,y),l="POSTPONE"):l=V(m,p,y);null===A?ef(m,p):(A.pendingTasks--,4!==A.status&&(A.status=4,A.errorDigest=l,pf(m,A),A.parentFlushed&&m.clientRenderedBoundaries.push(A)));m.allPendingTasks--;0===m.allPendingTasks&&Af(m)}finally{}}}}g.splice(0,h);null!==
a.destination&&Cf(a,a.destination)}catch(G){V(a,G,{}),ef(a,G)}finally{Le=f,Se.current=c,Te.current=d,c===Me&&Td(b),T=e}}}
function Df(a,b,c,d){c.parentFlushed=!0;switch(c.status){case 0:c.id=a.nextSegmentId++;case 5:return d=c.id,c.lastPushedText=!1,c.textEmbedded=!1,a=a.renderState,b.push('<template id="'),b.push(a.placeholderPrefix),a=d.toString(16),b.push(a),b.push('"></template>');case 1:c.status=2;var e=!0,f=c.chunks,g=0;c=c.children;for(var h=0;h<c.length;h++){for(e=c[h];g<e.index;g++)b.push(f[g]);e=Ef(a,b,e,d)}for(;g<f.length-1;g++)b.push(f[g]);g<f.length&&(e=b.push(f[g]));return e;default:throw Error("Aborted, errored or already flushed boundaries should not be flushed again. This is a bug in React.");
}}
function Ef(a,b,c,d){var e=c.boundary;if(null===e)return Df(a,b,c,d);e.parentFlushed=!0;if(4===e.status)return a.renderState.generateStaticMarkup||(e=e.errorDigest,b.push("\x3c!--$!--\x3e"),b.push("<template"),e&&(b.push(' data-dgst="'),e=B(e),b.push(e),b.push('"')),b.push("></template>")),Df(a,b,c,d),a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e"),a;if(1!==e.status)return 0===e.status&&(e.rootSegmentID=a.nextSegmentId++),0<e.completedSegments.length&&a.partialBoundaries.push(e),Fc(b,
a.renderState,e.rootSegmentID),d&&(e=e.fallbackState,e.styles.forEach(dd,d),e.stylesheets.forEach(ed,d)),Df(a,b,c,d),b.push("\x3c!--/$--\x3e");if(e.byteSize>a.progressiveChunkSize)return e.rootSegmentID=a.nextSegmentId++,a.completedBoundaries.push(e),Fc(b,a.renderState,e.rootSegmentID),Df(a,b,c,d),b.push("\x3c!--/$--\x3e");d&&(c=e.contentState,c.styles.forEach(dd,d),c.stylesheets.forEach(ed,d));a.renderState.generateStaticMarkup||b.push("\x3c!--$--\x3e");c=e.completedSegments;if(1!==c.length)throw Error("A previously unvisited boundary must have exactly one root segment. This is a bug in React.");
Ef(a,b,c[0],d);a=a.renderState.generateStaticMarkup?!0:b.push("\x3c!--/$--\x3e");return a}function Ff(a,b,c,d){Gc(b,a.renderState,c.parentFormatContext,c.id);Ef(a,b,c,d);return Hc(b,c.parentFormatContext)}
function Gf(a,b,c){for(var d=c.completedSegments,e=0;e<d.length;e++)Hf(a,b,c,d[e]);d.length=0;Qc(b,c.contentState,a.renderState);d=a.resumableState;a=a.renderState;e=c.rootSegmentID;c=c.contentState;var f=a.stylesToHoist;a.stylesToHoist=!1;var g=0===d.streamingFormat;g?(b.push(a.startInlineScript),f?0===(d.instructions&2)?(d.instructions|=10,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
0===(d.instructions&8)?(d.instructions|=8,b.push('$RM=new Map;\n$RR=function(r,t,w){for(var u=$RC,n=$RM,p=new Map,q=document,g,b,h=q.querySelectorAll("link[data-precedence],style[data-precedence]"),v=[],k=0;b=h[k++];)"not all"===b.getAttribute("media")?v.push(b):("LINK"===b.tagName&&n.set(b.getAttribute("href"),b),p.set(b.dataset.precedence,g=b));b=0;h=[];var l,a;for(k=!0;;){if(k){var f=w[b++];if(!f){k=!1;b=0;continue}var c=!1,m=0;var d=f[m++];if(a=n.get(d)){var e=a._p;c=!0}else{a=q.createElement("link");a.href=d;a.rel="stylesheet";for(a.dataset.precedence=\nl=f[m++];e=f[m++];)a.setAttribute(e,f[m++]);e=a._p=new Promise(function(x,y){a.onload=x;a.onerror=y});n.set(d,a)}d=a.getAttribute("media");!e||"l"===e.s||d&&!matchMedia(d).matches||h.push(e);if(c)continue}else{a=v[b++];if(!a)break;l=a.getAttribute("data-precedence");a.removeAttribute("media")}c=p.get(l)||g;c===g&&(g=a);p.set(l,a);c?c.parentNode.insertBefore(a,c.nextSibling):(c=q.head,c.insertBefore(a,c.firstChild))}Promise.all(h).then(u.bind(null,r,t,""),u.bind(null,r,t,"Resource failed to load"))};$RR("')):
b.push('$RR("'):0===(d.instructions&2)?(d.instructions|=2,b.push('$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("')):
b.push('$RC("')):f?b.push('<template data-rri="" data-bid="'):b.push('<template data-rci="" data-bid="');d=e.toString(16);b.push(a.boundaryPrefix);b.push(d);g?b.push('","'):b.push('" data-sid="');b.push(a.segmentPrefix);b.push(d);f?g?(b.push('",'),Wc(b,c)):(b.push('" data-sty="'),Xc(b,c)):g&&b.push('"');d=g?b.push(")\x3c/script>"):b.push('"></template>');return Ec(b,a)&&d}
function Hf(a,b,c,d){if(2===d.status)return!0;var e=c.contentState,f=d.id;if(-1===f){if(-1===(d.id=c.rootSegmentID))throw Error("A root segment ID must have been assigned by now. This is a bug in React.");return Ff(a,b,d,e)}if(f===c.rootSegmentID)return Ff(a,b,d,e);Ff(a,b,d,e);c=a.resumableState;a=a.renderState;(d=0===c.streamingFormat)?(b.push(a.startInlineScript),0===(c.instructions&1)?(c.instructions|=1,b.push('$RS=function(a,b){a=document.getElementById(a);b=document.getElementById(b);for(a.parentNode.removeChild(a);a.firstChild;)b.parentNode.insertBefore(a.firstChild,b);b.parentNode.removeChild(b)};$RS("')):
b.push('$RS("')):b.push('<template data-rsi="" data-sid="');b.push(a.segmentPrefix);f=f.toString(16);b.push(f);d?b.push('","'):b.push('" data-pid="');b.push(a.placeholderPrefix);b.push(f);b=d?b.push('")\x3c/script>'):b.push('"></template>');return b}
function Cf(a,b){try{var c,d=a.completedRootSegment;if(null!==d)if(5!==d.status&&0===a.pendingRootTasks){var e=a.renderState;if((0!==a.allPendingTasks||null!==a.trackedPostpones)&&e.externalRuntimeScript){var f=e.externalRuntimeScript,g=a.resumableState,h=f.src,k=f.chunks;g.scriptResources.hasOwnProperty(h)||(g.scriptResources[h]=null,e.scripts.add(k))}var m=e.htmlChunks,n=e.headChunks,l;if(m){for(l=0;l<m.length;l++)b.push(m[l]);if(n)for(l=0;l<n.length;l++)b.push(n[l]);else{var q=P("head");b.push(q);
b.push(">")}}else if(n)for(l=0;l<n.length;l++)b.push(n[l]);var r=e.charsetChunks;for(l=0;l<r.length;l++)b.push(r[l]);r.length=0;e.preconnects.forEach(R,b);e.preconnects.clear();var C=e.viewportChunks;for(l=0;l<C.length;l++)b.push(C[l]);C.length=0;e.fontPreloads.forEach(R,b);e.fontPreloads.clear();e.highImagePreloads.forEach(R,b);e.highImagePreloads.clear();e.styles.forEach(Tc,b);var v=e.importMapChunks;for(l=0;l<v.length;l++)b.push(v[l]);v.length=0;e.bootstrapScripts.forEach(R,b);e.scripts.forEach(R,
b);e.scripts.clear();e.bulkPreloads.forEach(R,b);e.bulkPreloads.clear();var x=e.hoistableChunks;for(l=0;l<x.length;l++)b.push(x[l]);x.length=0;if(m&&null===n){var w=vc("head");b.push(w)}Ef(a,b,d,null);a.completedRootSegment=null;Ec(b,a.renderState)}else return;var p=a.renderState;d=0;var F=p.viewportChunks;for(d=0;d<F.length;d++)b.push(F[d]);F.length=0;p.preconnects.forEach(R,b);p.preconnects.clear();p.fontPreloads.forEach(R,b);p.fontPreloads.clear();p.highImagePreloads.forEach(R,b);p.highImagePreloads.clear();
p.styles.forEach(Vc,b);p.scripts.forEach(R,b);p.scripts.clear();p.bulkPreloads.forEach(R,b);p.bulkPreloads.clear();var E=p.hoistableChunks;for(d=0;d<E.length;d++)b.push(E[d]);E.length=0;var t=a.clientRenderedBoundaries;for(c=0;c<t.length;c++){var y=t[c];p=b;var A=a.resumableState,G=a.renderState,rb=y.rootSegmentID,ra=y.errorDigest,Q=y.errorMessage,ca=y.errorComponentStack,S=0===A.streamingFormat;S?(p.push(G.startInlineScript),0===(A.instructions&4)?(A.instructions|=4,p.push('$RX=function(b,c,d,e){var a=document.getElementById(b);a&&(b=a.previousSibling,b.data="$!",a=a.dataset,c&&(a.dgst=c),d&&(a.msg=d),e&&(a.stck=e),b._reactRetry&&b._reactRetry())};;$RX("')):
p.push('$RX("')):p.push('<template data-rxi="" data-bid="');p.push(G.boundaryPrefix);var sa=rb.toString(16);p.push(sa);S&&p.push('"');if(ra||Q||ca)if(S){p.push(",");var ta=Jc(ra||"");p.push(ta)}else{p.push('" data-dgst="');var sb=B(ra||"");p.push(sb)}if(Q||ca)if(S){p.push(",");var tb=Jc(Q||"");p.push(tb)}else{p.push('" data-msg="');var ub=B(Q||"");p.push(ub)}if(ca)if(S){p.push(",");var vb=Jc(ca);p.push(vb)}else{p.push('" data-stck="');var wb=B(ca);p.push(wb)}if(S?!p.push(")\x3c/script>"):!p.push('"></template>')){a.destination=
null;c++;t.splice(0,c);return}}t.splice(0,c);var ia=a.completedBoundaries;for(c=0;c<ia.length;c++)if(!Gf(a,b,ia[c])){a.destination=null;c++;ia.splice(0,c);return}ia.splice(0,c);var Y=a.partialBoundaries;for(c=0;c<Y.length;c++){var ua=Y[c];a:{t=a;y=b;var ja=ua.completedSegments;for(A=0;A<ja.length;A++)if(!Hf(t,y,ua,ja[A])){A++;ja.splice(0,A);var ka=!1;break a}ja.splice(0,A);ka=Qc(y,ua.contentState,t.renderState)}if(!ka){a.destination=null;c++;Y.splice(0,c);return}}Y.splice(0,c);var M=a.completedBoundaries;
for(c=0;c<M.length;c++)if(!Gf(a,b,M[c])){a.destination=null;c++;M.splice(0,c);return}M.splice(0,c)}finally{0===a.allPendingTasks&&0===a.pingedTasks.length&&0===a.clientRenderedBoundaries.length&&0===a.completedBoundaries.length&&(a.flushScheduled=!1,null===a.trackedPostpones&&(c=a.resumableState,c.hasBody&&(Y=vc("body"),b.push(Y)),c.hasHtml&&(c=vc("html"),b.push(c))),b.push(null),a.destination=null)}}
function If(a){a.flushScheduled=null!==a.destination;$e(a);null===a.trackedPostpones&&Bf(a,0===a.pendingRootTasks)}function ad(a){if(!1===a.flushScheduled&&0===a.pingedTasks.length&&null!==a.destination){a.flushScheduled=!0;var b=a.destination;b?Cf(a,b):a.flushScheduled=!1}}function Jf(a,b){if(1===a.status)a.status=2,b.destroy(a.fatalError);else if(2!==a.status&&null===a.destination){a.destination=b;try{Cf(a,b)}catch(c){V(a,c,{}),ef(a,c)}}}
function Kf(a,b){try{var c=a.abortableTasks;if(0<c.size){var d=void 0===b?Error("The render was aborted by the server without a reason."):b;c.forEach(function(e){return yf(e,a,d)});c.clear()}null!==a.destination&&Cf(a,a.destination)}catch(e){V(a,e,{}),ef(a,e)}}function uf(a,b,c){if(null===b)c.rootNodes.push(a);else{var d=c.workingMap,e=d.get(b);void 0===e&&(e=[b[1],b[2],[],null],d.set(b,e),uf(e,b[0],c));e[2].push(a)}}function Lf(){}
function Mf(a,b,c,d){var e=!1,f=null,g="",h=!1;b=Xb(b?b.identifierPrefix:void 0,void 0);a=We(a,b,fd(b,c),Yb(),Infinity,Lf,void 0,function(){h=!0},void 0,void 0,void 0);If(a);Kf(a,d);Jf(a,{push:function(k){null!==k&&(g+=k);return!0},destroy:function(k){e=!0;f=k}});if(e&&f!==d)throw f;if(!h)throw Error("A component suspended while responding to synchronous input. This will cause the UI to be replaced with a loading indicator. To fix, updates that suspend should be wrapped with startTransition.");return g}
function Nf(a,b){a.prototype=Object.create(b.prototype);a.prototype.constructor=a;a.__proto__=b}var Of=function(a){function b(){var d=a.call(this,{})||this;d.request=null;d.startedFlowing=!1;return d}Nf(b,a);var c=b.prototype;c._destroy=function(d,e){Kf(this.request);e(d)};c._read=function(){this.startedFlowing&&Jf(this.request,this)};return b}(ea.Readable);function Pf(){}
function Qf(a,b){var c=new Of;b=Xb(b?b.identifierPrefix:void 0,void 0);var d=We(a,b,fd(b,!1),Yb(),Infinity,Pf,function(){c.startedFlowing=!0;Jf(d,c)},void 0,void 0,void 0);c.request=d;If(d);return c}exports.renderToNodeStream=function(a,b){return Qf(a,b)};exports.renderToStaticMarkup=function(a,b){return Mf(a,b,!0,'The server used "renderToStaticMarkup" which does not support Suspense. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.renderToStaticNodeStream=function(a,b){return Qf(a,b)};exports.renderToString=function(a,b){return Mf(a,b,!1,'The server used "renderToString" which does not support Suspense. If you intended for this Suspense boundary to render the fallback content on the server consider throwing an Error somewhere within the Suspense boundary. If you intended to have the server wait for the suspended component please switch to "renderToPipeableStream" which supports Suspense on the server')};
exports.version="18.3.0-experimental-14898b6a9-20240318";

//# sourceMappingURL=react-dom-server-legacy.node.production.min.js.map
