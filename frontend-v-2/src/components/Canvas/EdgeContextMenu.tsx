"use client";

import React, { useState } from "react";
import { Edge } from "reactflow";
import {
  Paper,
  Button,
  TextField,
  Box,
  MenuList,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from "@mui/material";
import { Delete as Trash2, Edit as Edit2 } from "@mui/icons-material";

interface EdgeContextMenuProps {
  x: number;
  y: number;
  edge: Edge;
  onClose: () => void;
  onRename: (edgeId: string, newLabel: string) => void;
  onDelete: (edgeId: string) => void;
}

export function EdgeContextMenu({
  x,
  y,
  edge,
  onClose,
  onRename,
  onDelete,
}: EdgeContextMenuProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(edge.data?.label || "");

  const handleRename = () => {
    onRename(edge.id, label);
    setIsEditing(false);
    onClose();
  };

  const handleDelete = () => {
    onDelete(edge.id);
    onClose();
  };

  return (
    <Paper
      sx={{
        position: "fixed",
        left: `${x}px`,
        top: `${y}px`,
        zIndex: 1000,
        borderRadius: 2,
        boxShadow: 3,
        border: 1,
        borderColor: "divider",
        p: 1,
        minWidth: 150,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {isEditing ? (
        <Box sx={{ display: "flex", gap: 1 }}>
          <TextField
            value={label}
            onChange={(e) => setLabel(e.target.value)}
            size="small"
            autoFocus
            onKeyDown={(e) => {
              if (e.key === "Enter") handleRename();
            }}
            sx={{ fontSize: "0.875rem" }}
          />
          <Button size="small" variant="contained" onClick={handleRename}>
            Save
          </Button>
        </Box>
      ) : (
        <MenuList dense>
          <MenuItem onClick={() => setIsEditing(true)}>
            <ListItemIcon>
              <Edit2 fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Rename" />
          </MenuItem>
          <MenuItem
            onClick={handleDelete}
            sx={{
              color: "error.main",
              "&:hover": {
                backgroundColor: "error.light",
                color: "error.dark",
              },
            }}
          >
            <ListItemIcon sx={{ color: "inherit" }}>
              <Trash2 fontSize="small" />
            </ListItemIcon>
            <ListItemText primary="Delete" />
          </MenuItem>
        </MenuList>
      )}
    </Paper>
  );
}
