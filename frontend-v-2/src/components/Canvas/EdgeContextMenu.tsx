'use client';

import React, { useState } from 'react';
import { Edge } from 'reactflow';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2, Edit2 } from 'lucide-react';

interface EdgeContextMenuProps {
  x: number;
  y: number;
  edge: Edge;
  onClose: () => void;
  onRename: (edgeId: string, newLabel: string) => void;
  onDelete: (edgeId: string) => void;
}

export function EdgeContextMenu({
  x,
  y,
  edge,
  onClose,
  onRename,
  onDelete,
}: EdgeContextMenuProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [label, setLabel] = useState(edge.data?.label || '');

  const style = {
    position: 'fixed',
    left: `${x}px`,
    top: `${y}px`,
    zIndex: 1000,
  } as const;

  const handleRename = () => {
    onRename(edge.id, label);
    setIsEditing(false);
    onClose();
  };

  const handleDelete = () => {
    onDelete(edge.id);
    onClose();
  };

  return (
    <div
      className="bg-white rounded-lg shadow-lg border border-gray-200 p-2 min-w-[150px]"
      style={style}
      onClick={(e) => e.stopPropagation()}
    >
      {isEditing ? (
        <div className="flex gap-2">
          <Input
            value={label}
            onChange={(e) => setLabel(e.target.value)}
            className="text-sm"
            autoFocus
            onKeyPress={(e) => {
              if (e.key === 'Enter') handleRename();
            }}
          />
          <Button size="sm" onClick={handleRename}>
            Save
          </Button>
        </div>
      ) : (
        <div className="space-y-1">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start"
            onClick={() => setIsEditing(true)}
          >
            <Edit2 className="w-4 h-4 mr-2" />
            Rename
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
            onClick={handleDelete}
          >
            <Trash2 className="w-4 h-4 mr-2" />
            Delete
          </Button>
        </div>
      )}
    </div>
  );
} 