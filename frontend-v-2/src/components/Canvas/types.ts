// Canvas Types - Consolidated type definitions for Canvas components
import { Node, Edge, Connection, NodeChange, EdgeChange } from "reactflow";

// ============================================================================
// CORE TYPES
// ============================================================================

export type NodeType = 
  | "start" 
  | "event" 
  | "action" 
  | "integration" 
  | "decision" 
  | "approval" 
  | "dataInput" 
  | "dataOutput" 
  | "dataStore" 
  | "fork" 
  | "join" 
  | "end" 
  | "fallback" 
  | "humanTask" 
  | "notification" 
  | "audit" 
  | "tools";

export interface Position {
  x: number;
  y: number;
}

export interface NodeData {
  label?: string;
  description?: string;
  owner?: string;
  action?: string;
  context?: string;
  expectedBehavior?: string;
  conditions?: Array<string | {
    field: string;
    operator: "equals" | "notEquals" | "contains" | "greaterThan" | "lessThan";
    value: string;
  }>;
  inputs?: Array<{
    id: string;
    label?: string;
  }>;
  outputs?: Array<{
    id: string;
    label?: string;
  }>;
  toolConnections?: Array<{
    id: string;
    label?: string;
  }>;
}

export interface FlowConfig {
  id: string;
  name: string;
  description: string;
  config: {
    nodes: Array<{
      id: string;
      type: NodeType;
      position: Position;
      data: NodeData;
    }>;
    connections: Array<{
      from: string;
      to: string;
      fromHandle?: string;
      toHandle?: string;
      label?: string;
    }>;
  };
  createdAt: string;
  updatedAt: string;
}

// ============================================================================
// COMPONENT PROPS TYPES
// ============================================================================

export interface NodeCardProps {
  type: string;
  data: NodeData & {
    context?: string;
    expectedBehavior?: string;
  };
  onNodeUpdate?: (updatedData: NodeData) => void;
}

export interface NodeToolbarProps {
  onNodeAdd?: (nodeType: string) => void;
  onAddNode?: (nodeType: string) => void;
  onConfigClick: () => void;
  onSaveClick?: () => void;
  onLoadClick?: () => void;
  onToolsClick?: () => void;
  onAutoLayoutClick?: () => void;
  orientation?: "vertical" | "horizontal";
}

export interface EdgeContextMenuProps {
  x: number;
  y: number;
  edge: Edge;
  onClose: () => void;
  onRename: (edgeId: string, newLabel: string) => void;
  onDelete: (edgeId: string) => void;
}

export interface NodeContextMenuProps {
  children: React.ReactNode;
  onAddNode: (type: string, event?: React.MouseEvent) => void;
  onDeleteNode: (nodeId: string) => void;
}

export interface SaveFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string, flowId?: string) => void;
  existingFlows: FlowConfig[];
}

export interface LoadFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoad: (flowId: string) => void;
  onDelete: (flowId: string) => void;
  flows: FlowConfig[];
}

export interface ConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: {
    prompt: string;
    transcript: string;
    config: string;
  }) => void;
  initialConfig?: { 
    prompt: string; 
    transcript: string; 
    config: string; 
  };
  currentFlow?: FlowConfig;
}

// ============================================================================
// FLOW COMPONENT TYPES
// ============================================================================

export interface FlowProps {
  nodes: Node[];
  edges: Edge[];
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  onConnect: (connection: Connection) => void;
  onEdgesDelete: (edges: Edge[]) => void;
  onEdgeContextMenu: (event: React.MouseEvent, edge: Edge) => void;
  onNodeAdd: (nodeType: string) => void;
  onConfigClick: () => void;
  onSaveClick: () => void;
  onLoadClick: () => void;
  onNodesDelete: (nodes: Node[]) => void;
  onAutoLayout: () => void;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type ToolbarEdge = "top" | "right" | "bottom" | "left";

export interface ToolbarPosition {
  x: number;
  y: number;
  edge: ToolbarEdge;
}

export interface EdgeContextMenuState {
  x: number;
  y: number;
  edge: Edge;
}

export interface ContextMenuState {
  mouseX: number;
  mouseY: number;
}

// ============================================================================
// NODE CONFIGURATION TYPES
// ============================================================================

export interface NodeConfig {
  type: NodeType;
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  category: string;
}

export interface NodeCategory {
  [key: string]: NodeConfig[];
}

// ============================================================================
// CANVAS COMPONENT TYPES
// ============================================================================

export interface CanvasProps {
  onAddNode: (nodeType: NodeType, position?: Position) => void;
  calculateNodePositions?: (nodes: any[], connections: any[]) => any[];
}

// Re-export ReactFlow types for convenience
export type { Node, Edge, Connection, NodeChange, EdgeChange } from "reactflow";
export { Position as ReactFlowPosition } from "reactflow";
