"use client";

import React, { useState } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  FormLabel,
  Typography,
} from "@mui/material";
import { FlowConfig } from "@/lib/flows";

interface SaveFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string, flowId?: string) => void;
  existingFlows: FlowConfig[];
}

export function SaveFlowModal({
  isOpen,
  onClose,
  onSave,
  existingFlows,
}: SaveFlowModalProps) {
  const [flowName, setFlowName] = useState("");
  const [selectedFlowId, setSelectedFlowId] = useState<string | null>(null);

  const handleSave = () => {
    if (selectedFlowId) {
      // If a flow is selected, override it
      onSave(
        flowName ||
          existingFlows.find((f) => f.id === selectedFlowId)?.name ||
          "",
        selectedFlowId
      );
    } else if (flowName.trim()) {
      // If no flow is selected but we have a name, create new
      onSave(flowName.trim());
    }
    setFlowName("");
    setSelectedFlowId(null);
    onClose();
  };

  // Filter out the example flow from the list of overridable flows
  const overridableFlows = existingFlows.filter(
    (flow) => flow.id !== "example-flow"
  );

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Save Flow Configuration</DialogTitle>
      <DialogContent>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 3, pt: 1 }}>
          {/* New Flow Section */}
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <FormLabel>Create New Flow</FormLabel>
            <TextField
              value={flowName}
              onChange={(e) => {
                setFlowName(e.target.value);
                setSelectedFlowId(null); // Deselect any selected flow when typing
              }}
              placeholder="Enter flow name..."
              disabled={!!selectedFlowId}
              variant="outlined"
              size="small"
            />
          </Box>

          {/* Existing Flows Section */}
          {overridableFlows.length > 0 && (
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              <FormLabel>Or Override Existing Flow</FormLabel>
              <Box
                sx={{
                  height: 150,
                  width: "100%",
                  border: 1,
                  borderColor: "divider",
                  borderRadius: 1,
                  overflow: "auto",
                }}
              >
                <Box
                  sx={{
                    p: 1,
                    display: "flex",
                    flexDirection: "column",
                    gap: 1,
                  }}
                >
                  {overridableFlows.map((flow) => (
                    <Button
                      key={flow.id}
                      variant={
                        selectedFlowId === flow.id ? "contained" : "outlined"
                      }
                      sx={{
                        width: "100%",
                        justifyContent: "flex-start",
                        textAlign: "left",
                        flexDirection: "column",
                        alignItems: "flex-start",
                        py: 1,
                      }}
                      onClick={() => {
                        setSelectedFlowId(flow.id);
                        setFlowName(""); // Clear the name input when selecting a flow
                      }}
                    >
                      <Box
                        sx={{
                          display: "flex",
                          flexDirection: "column",
                          alignItems: "flex-start",
                        }}
                      >
                        <Typography
                          variant="body2"
                          sx={{ fontWeight: "medium" }}
                        >
                          {flow.name}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{ color: "text.secondary" }}
                        >
                          Last modified:{" "}
                          {new Date(flow.updatedAt).toLocaleString()}
                        </Typography>
                      </Box>
                    </Button>
                  ))}
                </Box>
              </Box>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ mt: 1 }}>
        <Button variant="outlined" onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          disabled={!flowName.trim() && !selectedFlowId}
        >
          {selectedFlowId ? "Override" : "Save New"}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
