"use client";

import React, { useCallback, useState } from "react";
import { Menu, MenuItem, Box, Typography } from "@mui/material";
import { NodeType } from "@/types/nodes";

interface NodeContextMenuProps {
  children: React.ReactNode;
  onAddNode: (type: string, event?: React.MouseEvent) => void;
  onDeleteNode: (nodeId: string) => void;
}

export function NodeContextMenu({
  children,
  onAddNode,
  onDeleteNode,
}: NodeContextMenuProps) {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number;
    mouseY: number;
  } | null>(null);

  const handleContextMenu = useCallback(
    (event: React.MouseEvent) => {
      event.preventDefault();

      // Get the closest node element
      const nodeElement = (event.target as HTMLElement).closest(
        ".react-flow__node"
      );
      if (nodeElement) {
        setSelectedNodeId(nodeElement.getAttribute("data-id"));
      } else {
        setSelectedNodeId(null);
      }

      setContextMenu(
        contextMenu === null
          ? {
              mouseX: event.clientX + 2,
              mouseY: event.clientY - 6,
            }
          : null
      );
    },
    [contextMenu]
  );

  const handleClose = () => {
    setContextMenu(null);
  };

  const handleMenuItemClick = (action: () => void) => {
    action();
    handleClose();
  };

  return (
    <Box onContextMenu={handleContextMenu}>
      {children}
      <Menu
        open={contextMenu !== null}
        onClose={handleClose}
        anchorReference="anchorPosition"
        anchorPosition={
          contextMenu !== null
            ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
            : undefined
        }
        slotProps={{
          paper: {
            sx: { width: 256 },
          },
        }}
      >
        {selectedNodeId ? (
          // Show delete option if a node is selected
          <MenuItem
            onClick={() =>
              handleMenuItemClick(() => onDeleteNode(selectedNodeId))
            }
            sx={{
              color: "error.main",
              "&:hover": {
                backgroundColor: "error.light",
                color: "error.dark",
              },
            }}
          >
            <Typography>Delete Node</Typography>
          </MenuItem>
        ) : (
          // Show add node options if no node is selected
          <>
            <MenuItem
              onClick={() => handleMenuItemClick(() => onAddNode("start"))}
            >
              Add Start Node
            </MenuItem>
            <MenuItem
              onClick={() => handleMenuItemClick(() => onAddNode("action"))}
            >
              Add Action Node
            </MenuItem>
            {/* Add other node types as needed */}
          </>
        )}
      </Menu>
    </Box>
  );
}
