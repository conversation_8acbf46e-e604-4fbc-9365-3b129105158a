"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogActions,
  Typography,
  Button,
  Box,
  CircularProgress,
} from "@mui/material";

interface FlowValidityModalProps {
  isOpen: boolean;
  onClose: () => void;
  validityReport: string;
  isValidating?: boolean;
}

export function FlowValidityModal({
  isOpen,
  onClose,
  validityReport,
  isValidating = false,
}: FlowValidityModalProps) {
  // Determine status icon and color based on report content
  const getStatusInfo = () => {
    if (isValidating) {
      return {
        icon: "⏳",
        title: "Validating Workflow",
        className: "text-amber-500",
      };
    } else if (validityReport.includes("❌")) {
      return {
        icon: "❌",
        title: "Validation Failed",
        className: "text-red-500",
      };
    } else if (validityReport.includes("⚠️")) {
      return {
        icon: "⚠️",
        title: "Validation Warnings",
        className: "text-amber-500",
      };
    } else {
      return {
        icon: "✅",
        title: "Validation Passed",
        className: "text-green-500",
      };
    }
  };

  const { icon, title, className } = getStatusInfo();

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <span>{icon}</span>
          <Typography
            variant="h6"
            component="span"
            sx={{
              color:
                className === "text-red-500"
                  ? "error.main"
                  : className === "text-amber-500"
                  ? "warning.main"
                  : "success.main",
            }}
          >
            {title}
          </Typography>
          {isValidating && <CircularProgress size={20} sx={{ ml: 1 }} />}
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          Workflow validation results using Groq LLM analysis
        </Typography>
      </DialogTitle>

      <DialogContent
        sx={{ minHeight: 200, maxHeight: "60vh", overflow: "auto" }}
      >
        {isValidating ? (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              py: 4,
            }}
          >
            <CircularProgress sx={{ mb: 2 }} />
            <Typography
              variant="body2"
              color="text.secondary"
              textAlign="center"
            >
              Analyzing workflow against validation rules...
            </Typography>
          </Box>
        ) : (
          <Box
            sx={{
              p: 2,
              bgcolor: "grey.100",
              borderRadius: 1,
              overflow: "auto",
              "& strong": { fontWeight: "bold" },
            }}
          >
            <Typography
              variant="body2"
              component="div"
              dangerouslySetInnerHTML={{
                __html: validityReport
                  .replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>")
                  .replace(/\n/g, "<br />"),
              }}
            />
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="outlined" disabled={isValidating}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}
