'use client';

import React from 'react';
import { Drawer } from '@/components/ui/drawer';
import { Button } from '@/components/ui/button';

interface CommandControlDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CommandControlDrawer({ isOpen, onClose }: CommandControlDrawerProps) {
  return (
    <Drawer isOpen={isOpen} onClose={onClose} side="right" width="40%" topOffset="64px">
      <div className="flex flex-col h-full">
        <div className="flex justify-between items-center border-b pb-4 mb-6">
          <h2 className="text-2xl font-bold">Command & Control</h2>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onClose} 
            className="h-8 px-3 rounded-md flex items-center gap-1 border border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M18 6 6 18" /><path d="m6 6 12 12" />
            </svg>
            <span>Close</span>
          </Button>
        </div>
        
        <div className="flex-1 overflow-y-auto pr-2">
          <div className="space-y-6">
            <section>
              <h3 className="text-lg font-semibold mb-3">Workflow Controls</h3>
              <div className="bg-muted/50 p-4 rounded-lg">
                <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
                <div className="grid grid-cols-2 gap-2 mt-4">
                  <Button variant="outline" className="w-full">Execute Flow</Button>
                  <Button variant="outline" className="w-full">Pause Flow</Button>
                </div>
              </div>
            </section>
            
            <section>
              <h3 className="text-lg font-semibold mb-3">System Status</h3>
              <div className="bg-muted/50 p-4 rounded-lg">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Active Nodes:</span>
                    <span className="font-medium">12</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Connections:</span>
                    <span className="font-medium">24</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory Usage:</span>
                    <span className="font-medium">128MB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Status:</span>
                    <span className="text-green-500 font-medium">Running</span>
                  </div>
                </div>
              </div>
            </section>
            
            <section>
              <h3 className="text-lg font-semibold mb-3">Recent Activity</h3>
              <div className="bg-muted/50 p-4 rounded-lg">
                <ul className="space-y-2">
                  <li className="pb-2 border-b">
                    <div className="text-sm font-medium">Node 'Process Data' executed</div>
                    <div className="text-xs text-muted-foreground">2 minutes ago</div>
                  </li>
                  <li className="pb-2 border-b">
                    <div className="text-sm font-medium">Connection established between 'Input' and 'Filter'</div>
                    <div className="text-xs text-muted-foreground">5 minutes ago</div>
                  </li>
                  <li className="pb-2 border-b">
                    <div className="text-sm font-medium">Workflow validation completed</div>
                    <div className="text-xs text-muted-foreground">10 minutes ago</div>
                  </li>
                  <li>
                    <div className="text-sm font-medium">System started</div>
                    <div className="text-xs text-muted-foreground">15 minutes ago</div>
                  </li>
                </ul>
              </div>
            </section>
            
            <section>
              <h3 className="text-lg font-semibold mb-3">Quick Actions</h3>
              <div className="grid grid-cols-2 gap-3">
                <Button variant="secondary" className="w-full">Export Data</Button>
                <Button variant="secondary" className="w-full">Import Data</Button>
                <Button variant="secondary" className="w-full">Clear Cache</Button>
                <Button variant="secondary" className="w-full">Reset Layout</Button>
              </div>
            </section>
          </div>
        </div>
      </div>
    </Drawer>
  );
}
