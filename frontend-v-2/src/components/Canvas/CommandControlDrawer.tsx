"use client";

import React from "react";
import {
  Drawer,
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemText,
  IconButton,
} from "@mui/material";
import { Close } from "@mui/icons-material";

interface CommandControlDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CommandControlDrawer({
  isOpen,
  onClose,
}: CommandControlDrawerProps) {
  return (
    <Drawer
      anchor="right"
      open={isOpen}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: '40%',
          minWidth: 400,
          top: 64,
          height: 'calc(100% - 64px)',
        },
      }}
    >
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', borderBottom: 1, borderColor: 'divider', pb: 2, mb: 3 }}>
          <Typography variant="h5" fontWeight="bold">Command & Control</Typography>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </Box>

        <Box sx={{ flex: 1, overflow: 'auto', pr: 1 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <Box>
              <Typography variant="h6" fontWeight="semibold" sx={{ mb: 2 }}>Workflow Controls</Typography>
              <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Typography variant="body2" sx={{ mb: 2 }}>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
                  eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
                  enim ad minim veniam, quis nostrud exercitation ullamco
                  laboris nisi ut aliquip ex ea commodo consequat.
                </Typography>
                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Button variant="outlined" fullWidth>
                      Execute Flow
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button variant="outlined" fullWidth>
                      Pause Flow
                    </Button>
                  </Grid>
                </Grid>
              </Paper>
            </Box>

            <Box>
              <Typography variant="h6" fontWeight="semibold" sx={{ mb: 2 }}>System Status</Typography>
              <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Active Nodes:</Typography>
                    <Typography variant="body2" fontWeight="medium">12</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Connections:</Typography>
                    <Typography variant="body2" fontWeight="medium">24</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Memory Usage:</Typography>
                    <Typography variant="body2" fontWeight="medium">128MB</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Status:</Typography>
                    <Typography variant="body2" fontWeight="medium" color="success.main">Running</Typography>
                  </Box>
                </Box>
              </Paper>
            </Box>

            <Box>
              <Typography variant="h6" fontWeight="semibold" sx={{ mb: 2 }}>Recent Activity</Typography>
              <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                <List dense>
                  <ListItem sx={{ px: 0, borderBottom: 1, borderColor: 'divider' }}>
                    <ListItemText
                      primary={<Typography variant="body2" fontWeight="medium">Node 'Process Data' executed</Typography>}
                      secondary={<Typography variant="caption" color="text.secondary">2 minutes ago</Typography>}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0, borderBottom: 1, borderColor: 'divider' }}>
                    <ListItemText
                      primary={<Typography variant="body2" fontWeight="medium">Connection established between 'Input' and 'Filter'</Typography>}
                      secondary={<Typography variant="caption" color="text.secondary">5 minutes ago</Typography>}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0, borderBottom: 1, borderColor: 'divider' }}>
                    <ListItemText
                      primary={<Typography variant="body2" fontWeight="medium">Workflow validation completed</Typography>}
                      secondary={<Typography variant="caption" color="text.secondary">10 minutes ago</Typography>}
                    />
                  </ListItem>
                  <ListItem sx={{ px: 0 }}>
                    <ListItemText
                      primary={<Typography variant="body2" fontWeight="medium">System started</Typography>}
                      secondary={<Typography variant="caption" color="text.secondary">15 minutes ago</Typography>}
                    />
                  </ListItem>
                </List>
              </Paper>
            </Box>

            <Box>
              <Typography variant="h6" fontWeight="semibold" sx={{ mb: 2 }}>Quick Actions</Typography>
              <Grid container spacing={1}>
                <Grid item xs={6}>
                  <Button variant="outlined" fullWidth size="small">
                    Export Data
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button variant="outlined" fullWidth size="small">
                    Import Data
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button variant="outlined" fullWidth size="small">
                    Clear Cache
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button variant="outlined" fullWidth size="small">
                    Reset Layout
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </Box>
        </Box>
      </Box>
    </Drawer>
  );
}
