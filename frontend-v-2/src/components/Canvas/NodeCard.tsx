"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  Dialog,
  DialogContent,
  DialogTitle,
  TextField,
  Button,
  Box,
  Typography,
  List,
  ListItem,
} from "@mui/material";
import { Handle, Position } from "reactflow";
import { Edit as Pencil } from "@mui/icons-material";
import { NodeCardProps, NodeData } from "./types";
import { nodeCategories } from "./nodeTypes";

export function NodeCard({ type, data, onNodeUpdate }: NodeCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedData, setEditedData] = useState<NodeData>({
    ...data,
    label: data.label || "",
    description: data.description || "",
    owner: data.owner || "",
    action: data.action || "",
    context: data.context || "",
    expectedBehavior: data.expectedBehavior || "",
    conditions: data.conditions || [],
  });

  useEffect(() => {
    setEditedData({
      ...data,
      label: data.label || "",
      description: data.description || "",
      owner: data.owner || "",
      action: data.action || "",
      context: data.context || "",
      expectedBehavior: data.expectedBehavior || "",
      conditions: data.conditions || [],
    });
  }, [data]);

  const handleSave = () => {
    if (onNodeUpdate) {
      const updatedData = {
        ...data,
        ...editedData,
      };

      console.log("NodeCard saving:", updatedData);
      onNodeUpdate(updatedData);
      setIsEditing(false);
    }
  };

  // Get node config for styling
  let finalConfig;

  // First check if this is specifically a 'tools' type node
  if (type === "tools" && nodeCategories.Tools) {
    finalConfig = nodeCategories.Tools[0];
  } else {
    // Otherwise look through all categories
    finalConfig = Object.values(nodeCategories)
      .flat()
      .find((node) => node.type === type);
  }

  const cardStyle = {
    borderColor: finalConfig?.color || "#e5e7eb",
    backgroundColor: finalConfig?.color ? `${finalConfig.color}10` : "white",
  };

  const handleStyle = (
    index: number,
    total: number,
    position: "left" | "right" | "bottom"
  ) => {
    // Base styles
    const baseStyle = {
      width: "24px",
      height: "24px",
      borderRadius: "12px",
      borderWidth: "2px",
      borderColor: finalConfig?.color || "#e5e7eb",
      backgroundColor: finalConfig?.color || "#e5e7eb", // Fill with the node's color
    };

    // Position-specific styles
    switch (position) {
      case "left":
        return {
          ...baseStyle,
          left: "-12px",
          top: `${25 + index * 50}%`,
        };
      case "right":
        return {
          ...baseStyle,
          right: "-12px",
          top: `${25 + index * 50}%`,
        };
      case "bottom":
        return {
          ...baseStyle,
          bottom: "-12px",
          left: "50%",
          transform: "translateX(-50%)",
        };
      default:
        return baseStyle;
    }
  };

  const [dialogOpen, setDialogOpen] = useState(false);

  // Card content remains the same until DialogContent
  return (
    <>
      <Card
        onClick={() => setDialogOpen(true)}
        sx={{
          minWidth: 200,
          cursor: "pointer",
          position: "relative",
          transition: "box-shadow 0.2s",
          "&:hover": {
            boxShadow: 3,
          },
          borderColor: cardStyle.borderColor,
          backgroundColor: cardStyle.backgroundColor,
        }}
      >
        {/* Tool Node: Special handling for tools node */}
        {type === "tools" ? (
          <>
            {/* Tools node should only have output connection points */}
            {/* Input handle removed as per requirement */}
            <Handle
              type="source"
              position={Position.Right}
              id="tool-output"
              style={handleStyle(0, 1, "right")}
              className="!border-solid"
            />
          </>
        ) : (
          <>
            {/* Input Handles - Not rendered for data nodes */}
            {data.inputs?.map(
              (input, index) =>
                // Skip rendering input handles for data nodes
                !["dataInput", "dataOutput", "dataStore"].includes(type) && (
                  <Handle
                    key={input.id}
                    type="target"
                    position={Position.Left}
                    id={input.id}
                    style={handleStyle(index, data.inputs!.length, "left")}
                    className="!border-solid"
                  />
                )
            )}

            {/* Output Handles */}
            {data.outputs?.map((output, index) => (
              <Handle
                key={output.id}
                type="source"
                position={Position.Right}
                id={output.id}
                style={handleStyle(index, data.outputs!.length, "right")}
                className="!border-solid"
              />
            ))}

            {/* Dynamic Bottom Handle for Tool Connections */}
            {data.toolConnections?.map((conn, index) => (
              <Handle
                key={`tool-${conn.id}`}
                type="target"
                position={Position.Bottom}
                id={`tool-input-${conn.id}`}
                style={handleStyle(
                  index,
                  data.toolConnections!.length,
                  "bottom"
                )}
                className="!border-solid"
              />
            ))}
          </>
        )}

        {/* Add padding to prevent terminal overlap */}
        <Box sx={{ p: 3 }}>
          {/* Node Icon */}
          <Box
            sx={{
              position: "absolute",
              left: -12,
              top: -12,
              backgroundColor: "white",
              borderRadius: "50%",
              p: 0.5,
              boxShadow: 1,
              border: 1,
              borderColor: "divider",
            }}
          >
            {finalConfig && (
              <finalConfig.icon
                fontSize="small"
                sx={{ color: finalConfig.color }}
              />
            )}
          </Box>

          {/* Node Title */}
          <Typography
            variant="h6"
            sx={{ fontWeight: "semibold", mb: 1, mt: 1 }}
          >
            {data.label || finalConfig?.label || type}
          </Typography>

          {/* Actor/Owner */}
          <Typography variant="body2" color="text.secondary">
            {data.owner || "Unassigned"}
          </Typography>
        </Box>
      </Card>

      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { height: "80vh", display: "flex", flexDirection: "column" },
        }}
      >
        <DialogTitle
          sx={{
            fontSize: "1.25rem",
            fontWeight: "semibold",
            display: "flex",
            alignItems: "center",
            gap: 1,
          }}
        >
          {finalConfig && (
            <finalConfig.icon
              fontSize="small"
              sx={{ color: finalConfig.color }}
            />
          )}
          {isEditing ? (
            <TextField
              value={editedData.label || ""}
              onChange={(e) =>
                setEditedData((prev) => ({
                  ...prev,
                  label: e.target.value,
                }))
              }
              variant="standard"
              placeholder={finalConfig?.label || type}
              sx={{ fontSize: "1.125rem", fontWeight: "semibold" }}
            />
          ) : (
            data.label || finalConfig?.label || type
          )}
        </DialogTitle>

        <DialogContent sx={{ flex: 1, overflow: "auto" }}>
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: 3,
              px: 3,
              pb: 3,
            }}
          >
            {/* Description */}
            <Box>
              <Typography
                variant="subtitle2"
                sx={{ fontWeight: "medium", mb: 1, color: "text.secondary" }}
              >
                Description
              </Typography>
              {isEditing ? (
                <TextField
                  value={editedData.description || ""}
                  onChange={(e) =>
                    setEditedData((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  multiline
                  rows={4}
                  size="small"
                  placeholder="Describe the purpose of this node..."
                  fullWidth
                />
              ) : (
                <Typography variant="body2">
                  {data.description || "No description provided"}
                </Typography>
              )}
            </Box>

            <Box
              sx={{
                display: "flex",
                flexDirection: "column",
                gap: 3,
                borderLeft: 1,
                borderRight: 1,
                borderColor: "divider",
                px: 3,
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontWeight: "semibold",
                  color: "text.primary",
                  borderBottom: 1,
                  borderColor: "divider",
                  pb: 1,
                }}
              >
                Configuration
              </Typography>

              {/* Context */}
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{ fontWeight: "medium", mb: 1, color: "text.secondary" }}
                >
                  Context
                </Typography>
                {isEditing ? (
                  <TextField
                    value={editedData.context || ""}
                    onChange={(e) =>
                      setEditedData((prev) => ({
                        ...prev,
                        context: e.target.value,
                      }))
                    }
                    multiline
                    rows={4}
                    size="small"
                    placeholder="Describe the context of this node..."
                    fullWidth
                  />
                ) : (
                  <Typography variant="body2">
                    {data.context || "No context provided"}
                  </Typography>
                )}
              </Box>

              {/* Expected Behavior */}
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{ fontWeight: "medium", mb: 1, color: "text.secondary" }}
                >
                  Expected Behavior
                </Typography>
                {isEditing ? (
                  <TextField
                    value={editedData.expectedBehavior || ""}
                    onChange={(e) =>
                      setEditedData((prev) => ({
                        ...prev,
                        expectedBehavior: e.target.value,
                      }))
                    }
                    multiline
                    rows={4}
                    size="small"
                    placeholder="Describe the expected behavior..."
                    fullWidth
                  />
                ) : (
                  <Typography variant="body2">
                    {data.expectedBehavior || "No expected behavior defined"}
                  </Typography>
                )}
              </Box>

              {/* Actor/Owner */}
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{ fontWeight: "medium", mb: 1, color: "text.secondary" }}
                >
                  Actor
                </Typography>
                {isEditing ? (
                  <TextField
                    value={editedData.owner || ""}
                    onChange={(e) =>
                      setEditedData((prev) => ({
                        ...prev,
                        owner: e.target.value,
                      }))
                    }
                    size="small"
                    fullWidth
                  />
                ) : (
                  <Typography variant="body2">
                    {data.owner || "Unassigned"}
                  </Typography>
                )}
              </Box>

              {/* Action */}
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{ fontWeight: "medium", mb: 1, color: "text.secondary" }}
                >
                  Action
                </Typography>
                {isEditing ? (
                  <TextField
                    value={editedData.action || ""}
                    onChange={(e) =>
                      setEditedData((prev) => ({
                        ...prev,
                        action: e.target.value,
                      }))
                    }
                    size="small"
                    fullWidth
                  />
                ) : (
                  <Typography variant="body2">
                    {data.action || "No action"}
                  </Typography>
                )}
              </Box>

              {/* Conditions */}
              {data.conditions && (
                <Box>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontWeight: "medium",
                      mb: 1,
                      color: "text.secondary",
                    }}
                  >
                    Conditions
                  </Typography>
                  {isEditing ? (
                    <TextField
                      value={
                        editedData.conditions
                          ?.map((condition) =>
                            typeof condition === "string"
                              ? condition
                              : `${condition.field} ${condition.operator} ${condition.value}`
                          )
                          .join("\n") || ""
                      }
                      onChange={(e) =>
                        setEditedData({
                          ...editedData,
                          conditions: e.target.value
                            .split("\n")
                            .filter((line) => line.trim())
                            .map((line) => {
                              const [
                                field = "",
                                operator = "equals",
                                value = "",
                              ] = line.split(" ");
                              return {
                                field,
                                operator: operator as
                                  | "equals"
                                  | "notEquals"
                                  | "contains"
                                  | "greaterThan"
                                  | "lessThan",
                                value,
                              };
                            }),
                        })
                      }
                      multiline
                      rows={4}
                      size="small"
                      placeholder="Enter conditions (format: field operator value)"
                      fullWidth
                    />
                  ) : (
                    <List dense>
                      {data.conditions.map((condition, index) => (
                        <ListItem key={index}>
                          <Typography variant="body2">
                            {condition.toString()}
                          </Typography>
                        </ListItem>
                      ))}
                    </List>
                  )}
                </Box>
              )}

              {/* Parameters */}
              <Box>
                <Typography
                  variant="subtitle2"
                  sx={{ fontWeight: "medium", mb: 1, color: "text.secondary" }}
                >
                  Parameters
                </Typography>
                {isEditing ? (
                  <Box
                    sx={{ display: "flex", flexDirection: "column", gap: 1 }}
                  >
                    {/* Add parameter editing UI here */}
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No parameters configured
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>
        </DialogContent>

        {isEditing && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              gap: 1,
              px: 3,
              py: 2,
              borderTop: 1,
              borderColor: "divider",
              mt: "auto",
            }}
          >
            <Button variant="outlined" onClick={() => setIsEditing(false)}>
              Cancel
            </Button>
            <Button variant="contained" onClick={handleSave}>
              Save Changes
            </Button>
          </Box>
        )}

        {!isEditing && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              px: 3,
              py: 2,
              borderTop: 1,
              borderColor: "divider",
              mt: "auto",
            }}
          >
            <Button variant="contained" onClick={() => setIsEditing(true)}>
              <Pencil fontSize="small" sx={{ mr: 1 }} />
              Edit
            </Button>
          </Box>
        )}
      </Dialog>
    </>
  );
}
