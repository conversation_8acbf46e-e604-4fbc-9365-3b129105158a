'use client';

import React, { useState, useMemo } from 'react';
// Using Sheet instead of Drawer as per implementation
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Integration, availableIntegrations, integrationCategories } from '@/types/integration';
import { Search, Plus, Settings } from 'lucide-react';
import { getIntegrationIcon, categoryColors } from '@/lib/integrationIcons';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { IntegrationConnectModal } from './IntegrationConnectModal';

interface IntegrationsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

type IntegrationStatus = 'connected' | 'setup_required' | 'not_connected';

interface IntegrationWithStatus {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  authType: string;
  status: IntegrationStatus;
}

export function IntegrationsDrawer({ isOpen, onClose }: IntegrationsDrawerProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState('all');
  const [integrations, setIntegrations] = useState<IntegrationWithStatus[]>([]);
  const [filteredIntegrations, setFilteredIntegrations] = useState<IntegrationWithStatus[]>([]);
  const [selectedIntegration, setSelectedIntegration] = useState<IntegrationWithStatus | null>(null);
  const [isConnectModalOpen, setIsConnectModalOpen] = useState(false);
  
  // Mock connected integrations for demo purposes
  const connectedIntegrationIds = [
    'google-drive', 'google-sheets', 'google-calendar', 'slack', 'openai'
  ];
  
  // Mock setup required integrations
  const setupRequiredIntegrationIds = [
    'dropbox', 'github', 'notion'
  ];
  
  // Initialize integrations with status when component mounts
  useMemo(() => {
    // Initialize integrations with status
    const integrationsWithStatus = availableIntegrations.map(integration => ({
      ...integration,
      status: connectedIntegrationIds.includes(integration.id)
        ? 'connected' as IntegrationStatus
        : setupRequiredIntegrationIds.includes(integration.id)
          ? 'setup_required' as IntegrationStatus
          : 'not_connected' as IntegrationStatus
    }));
    
    setIntegrations(integrationsWithStatus);
    setFilteredIntegrations(integrationsWithStatus);
  }, []);
  
  // Filter integrations based on search query and active tab
  useMemo(() => {
    const filtered = integrations.filter(integration => {
      const matchesSearch = searchQuery === '' || 
        integration.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        integration.description.toLowerCase().includes(searchQuery.toLowerCase());
      
      const matchesTab = 
        filter === 'all' || 
        (filter === 'connected' && integration.status === 'connected') ||
        (filter === 'category' && integration.category === filter) ||
        integration.category === filter;
      
      return matchesSearch && matchesTab;
    });
    
    setFilteredIntegrations(filtered);
  }, [searchQuery, filter, integrations]);
  
  const getStatusBadge = (status: IntegrationStatus) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200 text-xs py-0.5">Connected</Badge>;
      case 'setup_required':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200 text-xs py-0.5">Setup Required</Badge>;
      case 'not_connected':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200 text-xs py-0.5">Not Connected</Badge>;
    }
  };
  
  const handleConnect = (id: string) => {
    // Find the integration by id and open the connect modal
    const integration = integrations.find(i => i.id === id);
    if (integration) {
      setSelectedIntegration(integration);
      setIsConnectModalOpen(true);
    }
  };

  const handleConnectComplete = (integrationId: string, credentials: any) => {
    // Update the integration status to connected
    setIntegrations(prev => 
      prev.map(integration => 
        integration.id === integrationId 
          ? { ...integration, status: 'connected' as IntegrationStatus } 
          : integration
      )
    );

    // In a real app, you would save the credentials securely
    console.log(`Connected to ${integrationId} with credentials:`, credentials);
  };
  
  // Group integrations by category
  const integrationsByCategory = integrationCategories.map(category => {
    const categoryIntegrations = filteredIntegrations.filter(
      integration => integration.category === category.id
    );
    return {
      ...category,
      integrations: categoryIntegrations
    };
  }).filter(category => category.integrations.length > 0);
  
  // Count connected integrations
  const connectedIntegrationsCount = filteredIntegrations.filter(
    integration => integration.status === 'connected'
  ).length;
  
  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent className="flex flex-col h-full w-[40%] min-w-[500px] p-0" side="right">
          <div className="p-5 h-full flex flex-col">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-bold">Integrations</h2>
              <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8">
                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
                </svg>
              </Button>
            </div>
            
            <div className="relative mb-3">
              <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <Input 
                placeholder="Search integrations..." 
                className="pl-9 h-9 text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <Tabs defaultValue="all" className="flex-1 overflow-hidden flex flex-col">
              <TabsList className="flex w-full mb-4 border-b border-gray-200 space-x-6">
                <TabsTrigger 
                  value="all" 
                  onClick={() => setFilter('all')} 
                  className="border-b-2 border-transparent data-[state=active]:border-blue-600 px-1 pb-2 -mb-[1px] rounded-none"
                >
                  All
                </TabsTrigger>
                <TabsTrigger 
                  value="connected" 
                  onClick={() => setFilter('connected')}
                  className="border-b-2 border-transparent data-[state=active]:border-blue-600 px-1 pb-2 -mb-[1px] rounded-none"
                >
                  Connected
                </TabsTrigger>
                {integrationCategories.map(category => (
                  <TabsTrigger key={category.id} value={category.id} className="border-b-2 border-transparent data-[state=active]:border-blue-600 px-1 pb-2 -mb-[1px] rounded-none">{category.name}</TabsTrigger>
                ))}
              </TabsList>
              
              <div className="overflow-y-auto flex-1 pr-2 -mr-2">
                <TabsContent value="all" className="space-y-4 p-5">
                  <div className="grid gap-4">
                    <div className="flex items-center gap-2">
                      <Search className="h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search integrations..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="h-9"
                      />
                    </div>
                    <div className="grid gap-2">
                      {filteredIntegrations.map((integration) => (
                        <IntegrationCard
                          key={integration.id}
                          integration={integration}
                          onConnect={() => {
                            setSelectedIntegration(integration);
                            setIsConnectModalOpen(true);
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="connected" className="space-y-4 p-5">
                  <div className="grid gap-2">
                    {filteredIntegrations
                      .filter((i) => i.status === 'connected')
                      .map((integration) => (
                        <IntegrationCard
                          key={integration.id}
                          integration={integration}
                          onConnect={() => {
                            setSelectedIntegration(integration);
                            setIsConnectModalOpen(true);
                          }}
                        />
                      ))}
                  </div>
                </TabsContent>
                
                {integrationCategories.map(categoryId => (
                  <TabsContent key={categoryId.id} value={categoryId.id} className="space-y-4 p-5">
                    <div className="grid gap-2">
                      {filteredIntegrations
                        .filter((i) => i.category === categoryId.id)
                        .map((integration) => (
                          <IntegrationCard
                            key={integration.id}
                            integration={integration}
                            onConnect={() => {
                              setSelectedIntegration(integration);
                              setIsConnectModalOpen(true);
                            }}
                          />
                        ))}
                    </div>
                  </TabsContent>
                ))}
              </div>
            </Tabs>
            
            <div className="mt-auto pt-3 border-t">
              <div className="flex justify-between gap-2">
                <Button variant="outline" size="sm" className="text-blue-600 border-blue-200 hover:bg-blue-50 text-xs h-8">
                  <Plus className="mr-1.5" size={14} />
                  Add Custom Integration
                </Button>
                <Button variant="outline" size="sm" className="text-blue-600 border-blue-200 hover:bg-blue-50 text-xs h-8">
                  <Settings className="mr-1.5" size={14} />
                  Manage API Keys
                </Button>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {selectedIntegration && (
        <IntegrationConnectModal
          isOpen={isConnectModalOpen}
          onClose={() => setIsConnectModalOpen(false)}
          integration={selectedIntegration}
          onConnect={(id, credentials) => {
            console.log(`Connected to ${id} with:`, credentials);
            // Here you would actually connect to the integration
            // For now we'll just simulate it
            const updatedIntegrations = integrations.map(i => 
              i.id === id ? { ...i, status: 'connected' as const } : i
            );
            setIntegrations(updatedIntegrations);
            setIsConnectModalOpen(false);
          }}
        />
      )}
    </>
  );
}

interface IntegrationCardProps {
  integration: IntegrationWithStatus;
  onConnect: () => void;
}

function IntegrationCard({ integration, onConnect }: IntegrationCardProps) {
  const isConnected = integration.status === 'connected';
  
  return (
    <TooltipProvider>
      <div className={`border rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-all p-4 relative ${!isConnected ? 'bg-gray-50' : ''}`}>
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className={`flex items-center justify-center w-10 h-10 rounded-md ${isConnected ? `bg-${categoryColors[integration.category]}-50` : 'bg-gray-100'}`}>
              {getIntegrationIcon(integration.icon, 22, isConnected ? categoryColors[integration.category] : '#6B7280')}
            </div>
            <div>
              <h3 className="font-medium text-gray-900">{integration.name}</h3>
              <p className="text-xs text-gray-500 line-clamp-1 mt-0.5">{integration.description}</p>
            </div>
          </div>
          <div>
            {getStatusBadge(integration.status)}
          </div>
        </div>
        
        <div className="mt-3 flex justify-between items-center">
          <div className="text-xs text-gray-500">
            {integration.authType === 'oauth2' ? 'OAuth 2.0' : 
             integration.authType === 'api_key' ? 'API Key' : 
             integration.authType === 'basic_auth' ? 'Basic Auth' : 'Custom Auth'}
          </div>
          
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="outline"
                size="sm"
                onClick={onConnect}
                className="ml-auto text-blue-600 border-blue-200 hover:bg-blue-50"
              >
                {isConnected ? 'Manage' : 'Connect'}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{isConnected ? 'Manage this integration' : 'Connect to ' + integration.name}</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  );
}

function getStatusBadge(status: IntegrationStatus) {
  switch (status) {
    case 'connected':
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Connected</Badge>;
    case 'setup_required':
      return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Setup Required</Badge>;
    case 'not_connected':
      return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">Not Connected</Badge>;
  }
}
