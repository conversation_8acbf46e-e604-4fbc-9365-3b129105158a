"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  IconButton,
  Box,
  FormLabel,
} from "@mui/material";
import { Co<PERSON>, Wand2, <PERSON>, <PERSON>rk<PERSON> } from "lucide-react";
import { getAllFlows } from "@/lib/flows";

interface ConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: {
    prompt: string;
    transcript: string;
    config: string;
  }) => void;
  initialConfig?: { prompt: string; transcript: string; config: string };
  currentFlow?: any;
}

export function ConfigModal({
  isOpen,
  onClose,
  onSave,
  initialConfig,
  currentFlow,
}: ConfigModalProps) {
  const [prompt, setPrompt] = useState(
    initialConfig?.prompt ||
      "From the transcript, extract out the key business processes from our action-input perspective, and then generate the json output."
  );
  const [transcript, setTranscript] = useState(initialConfig?.transcript || "");
  const [config, setConfig] = useState(initialConfig?.config || "");
  const [isGenerating, setIsGenerating] = useState(false);
  const [showRainbow, setShowRainbow] = useState(false);

  // Update config when modal opens with current flow
  useEffect(() => {
    if (isOpen && currentFlow) {
      setConfig(JSON.stringify(currentFlow, null, 2));
    }
  }, [isOpen, currentFlow]);

  const handleSave = () => {
    try {
      const parsedConfig = JSON.parse(config);
      onSave({ prompt, transcript, config });
      onClose();
    } catch (error) {
      console.error("Invalid JSON configuration:", error);
    }
  };

  const generateGenericConfig = () => {
    const flows = getAllFlows();
    const exampleFlow = flows.find((flow) => flow.id === "example-flow");
    if (exampleFlow) {
      setConfig(JSON.stringify(exampleFlow.config, null, 2));
    }
  };

  const handleCopyConfig = () => {
    navigator.clipboard.writeText(config);
  };

  const handleClearConfig = () => {
    setConfig("");
  };

  const handleGenerateConfig = async () => {
    if (!transcript || !prompt) {
      console.error("Prompt and transcript are required");
      return;
    }

    setIsGenerating(true);
    try {
      // Simulate API call with timeout
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Dummy response
      const dummyConfig = {
        message: "Hello World",
        timestamp: new Date().toISOString(),
      };

      setConfig(JSON.stringify(dummyConfig, null, 2));

      // Trigger rainbow animation
      setShowRainbow(true);
      setTimeout(() => {
        setShowRainbow(false);
      }, 3000);
    } catch (error) {
      console.error("Error generating config:", error);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: "600px", display: "flex", flexDirection: "column" },
      }}
    >
      <DialogTitle>Workflow Configuration</DialogTitle>
      <DialogContent sx={{ flex: 1, display: "flex", gap: 2, minHeight: 0 }}>
        <Box sx={{ flex: 1, display: "flex", flexDirection: "column", gap: 1 }}>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <FormLabel htmlFor="prompt">Prompt</FormLabel>
            <TextField
              id="prompt"
              placeholder="Enter your prompt here..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              multiline
              rows={3}
              variant="outlined"
              sx={{ resize: "none" }}
            />
          </Box>
          <Box
            sx={{ display: "flex", flexDirection: "column", gap: 1, flex: 1 }}
          >
            <FormLabel htmlFor="transcript">Transcript</FormLabel>
            <TextField
              id="transcript"
              placeholder="Paste your transcript here..."
              value={transcript}
              onChange={(e) => setTranscript(e.target.value)}
              multiline
              variant="outlined"
              sx={{ flex: 1, "& .MuiInputBase-root": { height: "100%" } }}
            />
          </Box>
        </Box>
        <Box sx={{ flex: 1, display: "flex", flexDirection: "column", gap: 1 }}>
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <FormLabel htmlFor="config">Configuration</FormLabel>
          </Box>
          <Box sx={{ flex: 1, position: "relative" }}>
            <Box
              sx={{
                position: "absolute",
                top: 8,
                right: 8,
                display: "flex",
                gap: 1,
                zIndex: 10,
              }}
            >
              <IconButton
                size="small"
                onClick={handleCopyConfig}
                sx={{
                  backgroundColor: "white",
                  "&:hover": { backgroundColor: "grey.100" },
                  boxShadow: 1,
                }}
              >
                <Copy size={16} />
              </IconButton>
              <IconButton
                size="small"
                onClick={handleClearConfig}
                sx={{
                  backgroundColor: "white",
                  "&:hover": { backgroundColor: "grey.100" },
                  boxShadow: 1,
                }}
              >
                <X size={16} />
              </IconButton>
            </Box>
            <TextField
              id="config"
              placeholder="Enter your configuration here..."
              value={config}
              onChange={(e) => setConfig(e.target.value)}
              multiline
              variant="outlined"
              sx={{
                height: "100%",
                width: "100%",
                fontFamily: "monospace",
                transition: "all 0.3s",
                ...(showRainbow && {
                  "& .MuiOutlinedInput-root": {
                    borderWidth: 2,
                    borderColor: "primary.main",
                    animation: "rainbow 3s ease-in-out",
                  },
                }),
                "& .MuiInputBase-root": {
                  height: "100%",
                  alignItems: "flex-start",
                },
              }}
            />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions
        sx={{ display: "flex", width: "100%", alignItems: "center", p: 2 }}
      >
        <Box sx={{ flex: 1 }}>
          <Button
            variant="contained"
            onClick={handleGenerateConfig}
            disabled={isGenerating}
            startIcon={<Sparkles size={16} />}
            sx={{
              backgroundColor: "#f59e0b",
              "&:hover": { backgroundColor: "#d97706" },
              color: "white",
            }}
          >
            {isGenerating ? "Generating..." : "Generate Config"}
          </Button>
        </Box>
        <Box sx={{ display: "flex", gap: 1 }}>
          <Button
            variant="outlined"
            onClick={generateGenericConfig}
            startIcon={<Wand2 size={16} />}
          >
            Generate Generic Config
          </Button>
          <Button variant="outlined" onClick={onClose}>
            Cancel
          </Button>
          <Button variant="contained" onClick={handleSave}>
            Save Changes
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
}
