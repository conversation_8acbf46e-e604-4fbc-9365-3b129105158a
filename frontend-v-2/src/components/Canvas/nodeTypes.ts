import { NodeType } from "@/components/Canvas/types";
import {
  // 1. Trigger icons
  Zap,
  PlayCircle,
  Clock,
  Webhook,

  // 2. Processing icons
  Play,
  <PERSON><PERSON>,
  Timer,
  <PERSON>,

  // 3. Decision Making icons
  Git<PERSON>ranch,
  CheckSquare2,
  AlertTriangle,

  // 4. Data Handling icons
  Database,
  Binary,
  HardDrive,
  Folder,

  // 5. Flow Control icons
  Split,
  GitCommit,
  RotateCw,
  GitMerge as MergeIcon,

  // 6. End/Termination icons
  Square,
  XCircle,
  LifeBuoy,

  // 7. Special Function icons
  User,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";

export interface NodeTypeConfig {
  type: NodeType;
  label: string;
  color: string;
  icon: typeof Zap;
  category:
    | "Trigger"
    | "Processing"
    | "Decision"
    | "Data"
    | "Flow"
    | "Termination"
    | "Special"
    | "Tools";
  description: string;
}

const nodeTypes: NodeTypeConfig[] = [
  // 1. Trigger Nodes
  {
    type: "start",
    label: "Start",
    color: "#22c55e",
    icon: Zap,
    category: "Trigger",
    description: "Starting point of the workflow",
  },
  {
    type: "event",
    label: "Event",
    color: "#22c55e",
    icon: Clock,
    category: "Trigger",
    description: "Event-based trigger",
  },

  // 2. Processing Nodes
  {
    type: "action",
    label: "Process",
    color: "#8b5cf6",
    icon: Play,
    category: "Processing",
    description: "Execute a process or action",
  },
  {
    type: "integration",
    label: "Integration",
    color: "#8b5cf6",
    icon: Link,
    category: "Processing",
    description: "External system integration",
  },

  // 3. Decision Making Nodes
  {
    type: "decision",
    label: "Decision",
    color: "#f59e0b",
    icon: GitBranch,
    category: "Decision",
    description: "Branch based on conditions",
  },
  {
    type: "approval",
    label: "Approval",
    color: "#f59e0b",
    icon: CheckSquare2,
    category: "Decision",
    description: "Approval required",
  },

  // 4. Data Handling Nodes
  {
    type: "dataInput",
    label: "Data Input",
    color: "#3b82f6",
    icon: Binary,
    category: "Data",
    description: "Input data source",
  },
  {
    type: "dataOutput",
    label: "Data Output",
    color: "#3b82f6",
    icon: HardDrive,
    category: "Data",
    description: "Output data destination",
  },
  {
    type: "dataStore",
    label: "Data Store",
    color: "#3b82f6",
    icon: Binary, //Database,
    category: "Data",
    description: "Data storage operations",
  },

  // 5. Flow Control Nodes
  {
    type: "fork",
    label: "Fork",
    color: "#ec4899",
    icon: Split,
    category: "Flow",
    description: "Split into parallel paths",
  },
  {
    type: "join",
    label: "Join",
    color: "#ec4899",
    icon: MergeIcon,
    category: "Flow",
    description: "Join parallel paths",
  },

  // 6. End/Termination Nodes
  {
    type: "end",
    label: "End",
    color: "#ef4444",
    icon: Square,
    category: "Termination",
    description: "End of workflow",
  },
  {
    type: "fallback",
    label: "Fallback",
    color: "#ef4444",
    icon: LifeBuoy,
    category: "Termination",
    description: "Fallback handling",
  },

  // 7. Special Function Nodes
  {
    type: "humanTask",
    label: "Human Task",
    color: "#64748b",
    icon: User,
    category: "Special",
    description: "Manual intervention required",
  },
  {
    type: "notification",
    label: "Notification",
    color: "#64748b",
    icon: Bell,
    category: "Special",
    description: "Send notifications",
  },
  {
    type: "audit",
    label: "Audit",
    color: "#64748b",
    icon: ClipboardList,
    category: "Special",
    description: "Audit logging",
  },
];

// Group nodes by category
export const nodeCategories = {
  Trigger: nodeTypes.filter((node) => node.category === "Trigger"),
  Processing: nodeTypes.filter((node) => node.category === "Processing"),
  Decision: nodeTypes.filter((node) => node.category === "Decision"),
  Data: nodeTypes.filter((node) => node.category === "Data"),
  Flow: nodeTypes.filter((node) => node.category === "Flow"),
  Termination: nodeTypes.filter((node) => node.category === "Termination"),
  Special: nodeTypes.filter((node) => node.category === "Special"),
  Tools: [
    {
      type: "tools",
      label: "Tools",
      color: "#14b8a6", // teal-500
      icon: Wrench,
      category: "Tools",
      description: "Tools and utilities node",
    },
  ],
};

export default nodeTypes;
