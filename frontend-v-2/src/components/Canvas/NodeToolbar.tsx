"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Tooltip, Box, Paper, IconButton } from "@mui/material";
import {
  Settings,
  Save,
  FolderOpen,
  Build as Wrench,
  GridView as LayoutGrid,
  CheckCircle,
  Terminal as Command,
  Security as Shield,
  Warning as AlertTriangle,
  Link,
  Inventory as FileBox,
} from "@mui/icons-material";
import { nodeCategories } from "@/lib/nodeTypes";
import { useReactFlow } from "reactflow";
import { FlowValidityModal } from "./FlowValidityModal";
import { CommandControlDrawer } from "./CommandControlDrawer";
import { RedTeamDrawer } from "./RedTeamDrawer";
import { IntegrationsDrawer } from "./IntegrationsDrawer";
import { ResourcesDrawer } from "./ResourcesDrawer";
import { NodeToolbarProps, ToolbarEdge, ToolbarPosition } from "./types";

type Edge = ToolbarEdge;
type Position = ToolbarPosition;

export function NodeToolbar({
  onNodeAdd,
  onAddNode,
  onConfigClick,
  onSaveClick,
  onLoadClick,
  onAutoLayoutClick,
  orientation,
}: NodeToolbarProps) {
  const [isValidityModalOpen, setIsValidityModalOpen] = useState(false);
  const [validityReport, setValidityReport] = useState<string>(
    "✔️ All flows are valid!"
  );
  const [isValidating, setIsValidating] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState<Position>({
    x: 20,
    y: 20,
    edge: "top",
  });
  const [isCommandControlOpen, setIsCommandControlOpen] = useState(false);
  const [isRedTeamOpen, setIsRedTeamOpen] = useState(false);
  const [isIntegrationsOpen, setIsIntegrationsOpen] = useState(false);
  const [isResourcesOpen, setIsResourcesOpen] = useState(false);
  const toolbarRef = useRef<HTMLDivElement>(null);
  const reactFlowInstance = useReactFlow();

  // Import the Groq validation function
  const { validateWorkflowWithGroq } = require("@/lib/groqValidation");

  const handleValidityCheck = async () => {
    // Get the current flow nodes and edges
    const nodes = reactFlowInstance.getNodes();
    const edges = reactFlowInstance.getEdges();

    // Basic validation before sending to Groq
    if (nodes.length === 0) {
      setValidityReport("⚠️ Warning: No nodes found in the flow.");
      setIsValidityModalOpen(true);
      return;
    }

    setIsValidating(true);
    setValidityReport("⏳ Validating workflow with Groq...");
    setIsValidityModalOpen(true);

    try {
      // Call Groq validation
      const validationResult = await validateWorkflowWithGroq(nodes, edges);
      setValidityReport(validationResult.report);
    } catch (error) {
      console.error("Error during workflow validation:", error);
      setValidityReport(`❌ Error validating workflow: ${error.message}`);
    } finally {
      setIsValidating(false);
    }
  };
  const dragStartRef = useRef<{ x: number; y: number } | null>(null);
  const { getViewport } = useReactFlow();

  // Use either onNodeAdd or onAddNode based on which one is provided
  const handleNodeAdd = useCallback(
    (nodeType: string) => {
      if (onAddNode) {
        onAddNode(nodeType);
      } else if (onNodeAdd) {
        onNodeAdd(nodeType);
      }
    },
    [onAddNode, onNodeAdd]
  );

  const mainNodes = [
    {
      category: "Trigger" as const,
      type: "start",
      color: "bg-green-500 hover:bg-green-600",
    },
    {
      category: "Processing" as const,
      type: "action",
      color: "bg-purple-500 hover:bg-purple-600",
    },
    {
      category: "Tools" as const,
      type: "tools",
      color: "bg-teal-500 hover:bg-teal-600",
    },
    {
      category: "Decision" as const,
      type: "decision",
      color: "bg-amber-500 hover:bg-amber-600",
    },
    {
      category: "Data" as const,
      type: "dataStore",
      color: "bg-blue-500 hover:bg-blue-600",
    },
    {
      category: "Flow" as const,
      type: "fork",
      color: "bg-pink-500 hover:bg-pink-600",
    },
    {
      category: "Termination" as const,
      type: "end",
      color: "bg-red-500 hover:bg-red-600",
    },
    {
      category: "Special" as const,
      type: "humanTask",
      color: "bg-slate-500 hover:bg-slate-600",
    },
  ];

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!toolbarRef.current) return;
    setIsDragging(true);
    dragStartRef.current = { x: e.clientX, y: e.clientY };
    e.preventDefault();
  };

  const snapToEdge = (x: number, y: number): Position => {
    if (!toolbarRef.current) return { x, y, edge: "top" };

    const container = document
      .querySelector(".react-flow")
      ?.getBoundingClientRect();
    if (!container) return { x, y, edge: "top" };

    // Get the ReactFlow container's offset
    const reactFlowBounds = container;

    // Adjust coordinates relative to the ReactFlow container
    const adjustedX = x - reactFlowBounds.left;
    const adjustedY = y - reactFlowBounds.top;

    const toolbar = toolbarRef.current.getBoundingClientRect();
    const snapThreshold = 50;

    // Calculate distances using adjusted coordinates
    const distanceToTop = adjustedY;
    const distanceToRight = reactFlowBounds.width - (adjustedX + toolbar.width);
    const distanceToBottom =
      reactFlowBounds.height - (adjustedY + toolbar.height);
    const distanceToLeft = adjustedX;

    const distances = [
      { edge: "top" as Edge, distance: distanceToTop },
      { edge: "right" as Edge, distance: distanceToRight },
      { edge: "bottom" as Edge, distance: distanceToBottom },
      { edge: "left" as Edge, distance: distanceToLeft },
    ];

    const closestEdge = distances.reduce((prev, curr) =>
      curr.distance < prev.distance ? curr : prev
    );

    if (closestEdge.distance > snapThreshold) {
      return { x: adjustedX, y: adjustedY, edge: position.edge };
    }

    // Snap to edges with 20px padding
    switch (closestEdge.edge) {
      case "top":
        return { x: adjustedX, y: 20, edge: "top" };
      case "right":
        return {
          x: reactFlowBounds.width - toolbar.width - 20,
          y: adjustedY,
          edge: "right",
        };
      case "bottom":
        return {
          x: adjustedX,
          y: reactFlowBounds.height - toolbar.height - 20,
          edge: "bottom",
        };
      case "left":
        return { x: 20, y: adjustedY, edge: "left" };
    }
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !toolbarRef.current || !dragStartRef.current) return;

      const deltaX = e.clientX - dragStartRef.current.x;
      const deltaY = e.clientY - dragStartRef.current.y;

      // Use client coordinates for the calculation
      const snappedPosition = snapToEdge(e.clientX, e.clientY);
      setPosition(snappedPosition);
      dragStartRef.current = { x: e.clientX, y: e.clientY };
    };

    const handleMouseUp = () => {
      if (!isDragging || !toolbarRef.current) return;

      const toolbar = toolbarRef.current.getBoundingClientRect();
      const finalPosition = snapToEdge(toolbar.left, toolbar.top);
      setPosition(finalPosition);
      setIsDragging(false);
      dragStartRef.current = null;
    };

    if (isDragging) {
      window.addEventListener("mousemove", handleMouseMove);
      window.addEventListener("mouseup", handleMouseUp);
    }

    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging, position]);

  const getTooltipPlacement = (): "top" | "right" | "bottom" | "left" => {
    switch (position.edge) {
      case "top":
        return "bottom";
      case "right":
        return "left";
      case "bottom":
        return "top";
      case "left":
        return "right";
    }
  };

  // Set initial position to top-center after first render
  useEffect(() => {
    const updatePosition = () => {
      const container = document
        .querySelector(".react-flow")
        ?.getBoundingClientRect();
      if (container && toolbarRef.current) {
        const toolbarWidth = toolbarRef.current.offsetWidth;
        setPosition({
          x: (container.width - toolbarWidth) / 2, // Center horizontally
          y: 20, // 20px from top
          edge: "top" as const,
        });
      }
    };

    updatePosition();
    window.addEventListener("resize", updatePosition);
    return () => window.removeEventListener("resize", updatePosition);
  }, []);

  return (
    <>
      <Paper
        ref={toolbarRef}
        sx={{
          display: "flex",
          gap: 1,
          backgroundColor: "rgba(255, 255, 255, 0.5)",
          backdropFilter: "blur(4px)",
          p: 1,
          borderRadius: 2,
          boxShadow: 3,
          cursor: "move",
          position: "absolute",
          transition: "all 0.2s ease-in-out",
          left: position.x,
          top: position.y,
          touchAction: "none",
          zIndex: 5,
          flexDirection: "row",
        }}
        onMouseDown={handleMouseDown}
      >
        {mainNodes.map(({ category, type, color }) => {
          const categoryNodes = nodeCategories[category];
          const mainNode = categoryNodes[0];

          return (
            <Tooltip
              key={category}
              title={`${category} Node - Click to add ${mainNode.label}`}
              placement={getTooltipPlacement()}
            >
              <IconButton
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 2,
                  boxShadow: 2,
                  backgroundColor: color.includes("green")
                    ? "#22c55e"
                    : color.includes("purple")
                    ? "#a855f7"
                    : color.includes("teal")
                    ? "#14b8a6"
                    : color.includes("amber")
                    ? "#f59e0b"
                    : color.includes("blue")
                    ? "#3b82f6"
                    : color.includes("pink")
                    ? "#ec4899"
                    : color.includes("red")
                    ? "#ef4444"
                    : color.includes("slate")
                    ? "#64748b"
                    : "#6b7280",
                  color: "white",
                  "&:hover": {
                    backgroundColor: color.includes("green")
                      ? "#16a34a"
                      : color.includes("purple")
                      ? "#9333ea"
                      : color.includes("teal")
                      ? "#0f766e"
                      : color.includes("amber")
                      ? "#d97706"
                      : color.includes("blue")
                      ? "#2563eb"
                      : color.includes("pink")
                      ? "#db2777"
                      : color.includes("red")
                      ? "#dc2626"
                      : color.includes("slate")
                      ? "#475569"
                      : "#4b5563",
                  },
                }}
                onClick={() => handleNodeAdd(type)}
              >
                <mainNode.icon size={20} />
              </IconButton>
            </Tooltip>
          );
        })}

        <Tooltip title="Save Flow" placement={getTooltipPlacement()}>
          <IconButton
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              boxShadow: 2,
              backgroundColor: "#10b981",
              color: "white",
              "&:hover": {
                backgroundColor: "#059669",
              },
            }}
            onClick={onSaveClick}
          >
            <Save fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Load Flow" placement={getTooltipPlacement()}>
          <IconButton
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              boxShadow: 2,
              backgroundColor: "#3b82f6",
              color: "white",
              "&:hover": {
                backgroundColor: "#2563eb",
              },
            }}
            onClick={onLoadClick}
          >
            <FolderOpen fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title="Configuration" placement={getTooltipPlacement()}>
          <IconButton
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              boxShadow: 2,
              backgroundColor: "#6b7280",
              color: "white",
              "&:hover": {
                backgroundColor: "#4b5563",
              },
            }}
            onClick={onConfigClick}
          >
            <Settings fontSize="small" />
          </IconButton>
        </Tooltip>

        {/* Check Validity Icon */}
        <Tooltip title="Check Flow Validity" placement={getTooltipPlacement()}>
          <IconButton
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              boxShadow: 2,
              backgroundColor: "#84cc16",
              color: "white",
              "&:hover": {
                backgroundColor: "#65a30d",
              },
            }}
            onClick={() => handleValidityCheck()}
          >
            <CheckCircle fontSize="small" />
          </IconButton>
        </Tooltip>

        {/* Auto-Layout Icon */}
        <Tooltip title="Auto-Layout Diagram" placement={getTooltipPlacement()}>
          <IconButton
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              boxShadow: 2,
              backgroundColor: "#6366f1",
              color: "white",
              "&:hover": {
                backgroundColor: "#4f46e5",
              },
            }}
            onClick={onAutoLayoutClick}
          >
            <LayoutGrid fontSize="small" />
          </IconButton>
        </Tooltip>

        {/* Command & Control Icon */}
        <Tooltip title="Command & Control" placement={getTooltipPlacement()}>
          <IconButton
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              boxShadow: 2,
              backgroundColor: "#374151",
              color: "white",
              "&:hover": {
                backgroundColor: "#1f2937",
              },
            }}
            onClick={() => setIsCommandControlOpen(true)}
          >
            <Command fontSize="small" />
          </IconButton>
        </Tooltip>

        {/* REDTEAM Icon */}
        <Tooltip title="REDTEAM" placement={getTooltipPlacement()}>
          <IconButton
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              boxShadow: 2,
              backgroundColor: "#dc2626",
              color: "white",
              "&:hover": {
                backgroundColor: "#b91c1c",
              },
            }}
            onClick={() => setIsRedTeamOpen(true)}
          >
            <Shield fontSize="small" />
          </IconButton>
        </Tooltip>

        {/* Integrations Icon */}
        <Tooltip title="Integrations" placement={getTooltipPlacement()}>
          <IconButton
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              boxShadow: 2,
              backgroundColor: "#2563eb",
              color: "white",
              "&:hover": {
                backgroundColor: "#1d4ed8",
              },
            }}
            onClick={() => setIsIntegrationsOpen(true)}
          >
            <Link fontSize="small" />
          </IconButton>
        </Tooltip>

        {/* Resources Icon */}
        <Tooltip title="Resources" placement={getTooltipPlacement()}>
          <IconButton
            sx={{
              width: 40,
              height: 40,
              borderRadius: 2,
              boxShadow: 2,
              backgroundColor: "#059669",
              color: "white",
              "&:hover": {
                backgroundColor: "#047857",
              },
            }}
            onClick={() => setIsResourcesOpen(true)}
          >
            <FileBox fontSize="small" />
          </IconButton>
        </Tooltip>
      </Paper>

      <FlowValidityModal
        isOpen={isValidityModalOpen}
        onClose={() => setIsValidityModalOpen(false)}
        validityReport={validityReport}
        isValidating={isValidating}
      />
      <CommandControlDrawer
        isOpen={isCommandControlOpen}
        onClose={() => setIsCommandControlOpen(false)}
      />
      <RedTeamDrawer
        isOpen={isRedTeamOpen}
        onClose={() => setIsRedTeamOpen(false)}
      />
      <IntegrationsDrawer
        isOpen={isIntegrationsOpen}
        onClose={() => setIsIntegrationsOpen(false)}
      />
      <ResourcesDrawer
        isOpen={isResourcesOpen}
        onClose={() => setIsResourcesOpen(false)}
      />
    </>
  );
}
