'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipTrigger,
  TooltipProvider 
} from "@/components/ui/tooltip";
import { Settings, Save, FolderOpen, Wrench, LayoutGrid, CheckCircle, Command, Shield, AlertTriangle, Link, FileBox } from 'lucide-react';
import { nodeCategories } from "@/lib/nodeTypes";
import { cn } from "@/lib/utils";
import { useReactFlow } from 'reactflow';
import { FlowValidityModal } from './FlowValidityModal';
import { CommandControlDrawer } from './CommandControlDrawer';
import { RedTeamDrawer } from './RedTeamDrawer';
import { IntegrationsDrawer } from './IntegrationsDrawer';
import { ResourcesDrawer } from './ResourcesDrawer';

interface NodeToolbarProps {
  onNodeAdd?: (nodeType: string) => void;
  onAddNode?: (nodeType: string) => void;
  onConfigClick: () => void;
  onSaveClick?: () => void;
  onLoadClick?: () => void;
  onToolsClick?: () => void;
  onAutoLayoutClick?: () => void;
  orientation?: 'vertical' | 'horizontal';
}

type Edge = 'top' | 'right' | 'bottom' | 'left';

interface Position {
  x: number;
  y: number;
  edge: Edge;
}

export function NodeToolbar({ onNodeAdd, onAddNode, onConfigClick, onSaveClick, onLoadClick, onAutoLayoutClick, orientation }: NodeToolbarProps) {
  const [isValidityModalOpen, setIsValidityModalOpen] = useState(false);
  const [validityReport, setValidityReport] = useState<string>('✔️ All flows are valid!');
  const [isValidating, setIsValidating] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [position, setPosition] = useState<Position>({ x: 20, y: 20, edge: 'top' });
  const [isCommandControlOpen, setIsCommandControlOpen] = useState(false);
  const [isRedTeamOpen, setIsRedTeamOpen] = useState(false);
  const [isIntegrationsOpen, setIsIntegrationsOpen] = useState(false);
  const [isResourcesOpen, setIsResourcesOpen] = useState(false);
  const toolbarRef = useRef<HTMLDivElement>(null);
  const reactFlowInstance = useReactFlow();
  
  // Import the Groq validation function
  const { validateWorkflowWithGroq } = require('@/lib/groqValidation');
  
  const handleValidityCheck = async () => {
    // Get the current flow nodes and edges
    const nodes = reactFlowInstance.getNodes();
    const edges = reactFlowInstance.getEdges();
    
    // Basic validation before sending to Groq
    if (nodes.length === 0) {
      setValidityReport('⚠️ Warning: No nodes found in the flow.');
      setIsValidityModalOpen(true);
      return;
    }
    
    setIsValidating(true);
    setValidityReport('⏳ Validating workflow with Groq...');
    setIsValidityModalOpen(true);
    
    try {
      // Call Groq validation
      const validationResult = await validateWorkflowWithGroq(nodes, edges);
      setValidityReport(validationResult.report);
    } catch (error) {
      console.error('Error during workflow validation:', error);
      setValidityReport(`❌ Error validating workflow: ${error.message}`);
    } finally {
      setIsValidating(false);
    }
  };
  const dragStartRef = useRef<{ x: number; y: number } | null>(null);
  const { getViewport } = useReactFlow();

  // Use either onNodeAdd or onAddNode based on which one is provided
  const handleNodeAdd = useCallback((nodeType: string) => {
    if (onAddNode) {
      onAddNode(nodeType);
    } else if (onNodeAdd) {
      onNodeAdd(nodeType);
    }
  }, [onAddNode, onNodeAdd]);

  const mainNodes = [
    { category: 'Trigger' as const, type: 'start', color: 'bg-green-500 hover:bg-green-600' },
    { category: 'Processing' as const, type: 'action', color: 'bg-purple-500 hover:bg-purple-600' },
    { category: 'Tools' as const, type: 'tools', color: 'bg-teal-500 hover:bg-teal-600' },
    { category: 'Decision' as const, type: 'decision', color: 'bg-amber-500 hover:bg-amber-600' },
    { category: 'Data' as const, type: 'dataStore', color: 'bg-blue-500 hover:bg-blue-600' },
    { category: 'Flow' as const, type: 'fork', color: 'bg-pink-500 hover:bg-pink-600' },
    { category: 'Termination' as const, type: 'end', color: 'bg-red-500 hover:bg-red-600' },
    { category: 'Special' as const, type: 'humanTask', color: 'bg-slate-500 hover:bg-slate-600' },
    
  ];

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!toolbarRef.current) return;
    setIsDragging(true);
    dragStartRef.current = { x: e.clientX, y: e.clientY };
    e.preventDefault();
  };

  const snapToEdge = (x: number, y: number): Position => {
    if (!toolbarRef.current) return { x, y, edge: 'top' };

    const container = document.querySelector('.react-flow')?.getBoundingClientRect();
    if (!container) return { x, y, edge: 'top' };

    // Get the ReactFlow container's offset
    const reactFlowBounds = container;
    
    // Adjust coordinates relative to the ReactFlow container
    const adjustedX = x - reactFlowBounds.left;
    const adjustedY = y - reactFlowBounds.top;

    const toolbar = toolbarRef.current.getBoundingClientRect();
    const snapThreshold = 50;

    // Calculate distances using adjusted coordinates
    const distanceToTop = adjustedY;
    const distanceToRight = reactFlowBounds.width - (adjustedX + toolbar.width);
    const distanceToBottom = reactFlowBounds.height - (adjustedY + toolbar.height);
    const distanceToLeft = adjustedX;

    const distances = [
      { edge: 'top' as Edge, distance: distanceToTop },
      { edge: 'right' as Edge, distance: distanceToRight },
      { edge: 'bottom' as Edge, distance: distanceToBottom },
      { edge: 'left' as Edge, distance: distanceToLeft }
    ];

    const closestEdge = distances.reduce((prev, curr) => 
      curr.distance < prev.distance ? curr : prev
    );

    if (closestEdge.distance > snapThreshold) {
      return { x: adjustedX, y: adjustedY, edge: position.edge };
    }

    // Snap to edges with 20px padding
    switch (closestEdge.edge) {
      case 'top':
        return { x: adjustedX, y: 20, edge: 'top' };
      case 'right':
        return { x: reactFlowBounds.width - toolbar.width - 20, y: adjustedY, edge: 'right' };
      case 'bottom':
        return { x: adjustedX, y: reactFlowBounds.height - toolbar.height - 20, edge: 'bottom' };
      case 'left':
        return { x: 20, y: adjustedY, edge: 'left' };
    }
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !toolbarRef.current || !dragStartRef.current) return;
      
      const deltaX = e.clientX - dragStartRef.current.x;
      const deltaY = e.clientY - dragStartRef.current.y;
      
      // Use client coordinates for the calculation
      const snappedPosition = snapToEdge(e.clientX, e.clientY);
      setPosition(snappedPosition);
      dragStartRef.current = { x: e.clientX, y: e.clientY };
    };

    const handleMouseUp = () => {
      if (!isDragging || !toolbarRef.current) return;
      
      const toolbar = toolbarRef.current.getBoundingClientRect();
      const finalPosition = snapToEdge(toolbar.left, toolbar.top);
      setPosition(finalPosition);
      setIsDragging(false);
      dragStartRef.current = null;
    };

    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, position]);

  const getToolbarStyles = () => {
    const baseStyles = "flex gap-2 bg-white/50 backdrop-blur-sm p-2 rounded-lg shadow-lg cursor-move absolute transition-all duration-200 ease-in-out";
    const edgeStyles = {
      top: "flex-row",
      right: "flex-row",
      bottom: "flex-row",
      left: "flex-row"
    };

    return cn(baseStyles, edgeStyles[position.edge]);
  };

  // Set initial position to top-center after first render
  useEffect(() => {
    const updatePosition = () => {
      const container = document.querySelector('.react-flow')?.getBoundingClientRect();
      if (container && toolbarRef.current) {
        const toolbarWidth = toolbarRef.current.offsetWidth;
        setPosition({
          x: (container.width - toolbarWidth) / 2, // Center horizontally
          y: 20, // 20px from top
          edge: 'top' as const
        });
      }
    };

    updatePosition();
    window.addEventListener('resize', updatePosition);
    return () => window.removeEventListener('resize', updatePosition);
  }, []);

  const getTooltipSide = (): "top" | "right" | "bottom" | "left" => {
    switch (position.edge) {
      case 'top': return 'bottom';
      case 'right': return 'left';
      case 'bottom': return 'top';
      case 'left': return 'right';
    }
  };

  return (
    <TooltipProvider>
      <div
        ref={toolbarRef}
        className={getToolbarStyles()}
        style={{
          left: position.x,
          top: position.y,
          touchAction: 'none',
          position: 'absolute',
          zIndex: 5
        }}
        onMouseDown={handleMouseDown}
      >
        {mainNodes.map(({ category, type, color }) => {
          const categoryNodes = nodeCategories[category];
          const mainNode = categoryNodes[0];
          
          return (
            <Tooltip key={category} delayDuration={0}>
              <TooltipTrigger asChild>
                <Button
                  variant="default"
                  size="icon"
                  className={cn(
                    "w-10 h-10 rounded-lg shadow-md",
                    color
                  )}
                  onClick={() => handleNodeAdd(type)}
                >
                  <mainNode.icon className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent 
                side={getTooltipSide()}
                className="flex flex-col gap-2"
              >
                <p className="font-semibold">{category} Node</p>
                <p className="text-xs text-gray-500">Click to add {mainNode.label}</p>
              </TooltipContent>
            </Tooltip>
          );
        })}

        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="default"
              size="icon"
              className="w-10 h-10 rounded-lg shadow-md bg-emerald-500 hover:bg-emerald-600"
              onClick={onSaveClick}
            >
              <Save className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={getTooltipSide()}>
            <p>Save Flow</p>
          </TooltipContent>
        </Tooltip>

        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="default"
              size="icon"
              className="w-10 h-10 rounded-lg shadow-md bg-blue-500 hover:bg-blue-600"
              onClick={onLoadClick}
            >
              <FolderOpen className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={getTooltipSide()}>
            <p>Load Flow</p>
          </TooltipContent>
        </Tooltip>

        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="default"
              size="icon"
              className="w-10 h-10 rounded-lg shadow-md bg-gray-500 hover:bg-gray-600"
              onClick={onConfigClick}
            >
              <Settings className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={getTooltipSide()}>
            <p>Configuration</p>
          </TooltipContent>
        </Tooltip>

        {/* Check Validity Icon */}
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="default"
              size="icon"
              className="w-10 h-10 rounded-lg shadow-md bg-lime-500 hover:bg-lime-600"
              onClick={() => handleValidityCheck()}
            >
              <CheckCircle className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={getTooltipSide()}>
            <p>Check Flow Validity</p>
          </TooltipContent>
        </Tooltip>

        {/* Auto-Layout Icon */}
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="default"
              size="icon"
              className="w-10 h-10 rounded-lg shadow-md bg-indigo-500 hover:bg-indigo-600"
              onClick={onAutoLayoutClick}
            >
              <LayoutGrid className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={getTooltipSide()}>
            <p>Auto-Layout Diagram</p>
          </TooltipContent>
        </Tooltip>

        {/* Command & Control Icon */}
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="default"
              size="icon"
              className="w-10 h-10 rounded-lg shadow-md bg-slate-700 hover:bg-slate-800"
              onClick={() => setIsCommandControlOpen(true)}
            >
              <Command className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={getTooltipSide()}>
            <p>Command & Control</p>
          </TooltipContent>
        </Tooltip>

        {/* REDTEAM Icon */}
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="default"
              size="icon"
              className="w-10 h-10 rounded-lg shadow-md bg-red-600 hover:bg-red-700"
              onClick={() => setIsRedTeamOpen(true)}
            >
              <Shield className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={getTooltipSide()}>
            <p>REDTEAM</p>
          </TooltipContent>
        </Tooltip>

        {/* Integrations Icon */}
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="default"
              size="icon"
              className="w-10 h-10 rounded-lg shadow-md bg-blue-600 hover:bg-blue-700"
              onClick={() => setIsIntegrationsOpen(true)}
            >
              <Link className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={getTooltipSide()}>
            <p>Integrations</p>
          </TooltipContent>
        </Tooltip>

        {/* Resources Icon */}
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="default"
              size="icon"
              className="w-10 h-10 rounded-lg shadow-md bg-emerald-600 hover:bg-emerald-700"
              onClick={() => setIsResourcesOpen(true)}
            >
              <FileBox className="h-5 w-5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent side={getTooltipSide()}>
            <p>Resources</p>
          </TooltipContent>
        </Tooltip>

      </div>
      <FlowValidityModal 
        isOpen={isValidityModalOpen} 
        onClose={() => setIsValidityModalOpen(false)} 
        validityReport={validityReport}
        isValidating={isValidating}
      />
      <CommandControlDrawer
        isOpen={isCommandControlOpen}
        onClose={() => setIsCommandControlOpen(false)}
      />
      <RedTeamDrawer
        isOpen={isRedTeamOpen}
        onClose={() => setIsRedTeamOpen(false)}
      />
      <IntegrationsDrawer
        isOpen={isIntegrationsOpen}
        onClose={() => setIsIntegrationsOpen(false)}
      />
      <ResourcesDrawer
        isOpen={isResourcesOpen}
        onClose={() => setIsResourcesOpen(false)}
      />
    </TooltipProvider>
  );
} 