"use client";

import React, { useState } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
} from "@mui/material";
import { FlowConfig } from "@/lib/flows";
import { Delete as Trash2 } from "@mui/icons-material";

interface LoadFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoad: (flowId: string) => void;
  onDelete: (flowId: string) => void;
  flows: FlowConfig[];
}

export function LoadFlowModal({
  isOpen,
  onClose,
  onLoad,
  onDelete,
  flows,
}: LoadFlowModalProps) {
  const [flowToDelete, setFlowToDelete] = useState<string | null>(null);

  const handleDelete = (flowId: string) => {
    onDelete(flowId);
    setFlowToDelete(null);
  };

  return (
    <>
      <Dialog open={isOpen} onClose={onClose} maxWidth="sm" fullWidth>
        <DialogTitle>Load Flow Configuration</DialogTitle>
        <DialogContent>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ mb: 2 }}
            id="load-flow-description"
          >
            Select a flow configuration to load or delete.
          </Typography>
          <Box
            sx={{
              height: 300,
              overflow: "auto",
              pr: 1,
            }}
          >
            <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
              {flows?.map((flow) => (
                <Box
                  key={flow.id}
                  sx={{ display: "flex", alignItems: "center", gap: 1 }}
                >
                  <Button
                    variant="outlined"
                    sx={{
                      flex: 1,
                      justifyContent: "flex-start",
                      textAlign: "left",
                      flexDirection: "column",
                      alignItems: "flex-start",
                      py: 1,
                    }}
                    onClick={() => {
                      if (flow.id) {
                        onLoad(flow.id);
                        onClose();
                      }
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "flex-start",
                      }}
                    >
                      <Typography variant="body2" sx={{ fontWeight: "medium" }}>
                        {flow.name}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Last modified:{" "}
                        {new Date(flow.updatedAt).toLocaleString()}
                      </Typography>
                    </Box>
                  </Button>
                  {flow.id !== "example-flow" && (
                    <IconButton
                      size="small"
                      sx={{
                        flexShrink: 0,
                        color: "error.main",
                        "&:hover": {
                          color: "error.dark",
                          backgroundColor: "error.light",
                          opacity: 0.1,
                        },
                      }}
                      onClick={() => setFlowToDelete(flow.id)}
                    >
                      <Trash2 fontSize="small" />
                    </IconButton>
                  )}
                </Box>
              ))}
            </Box>
          </Box>
        </DialogContent>
      </Dialog>

      <Dialog
        open={!!flowToDelete}
        onClose={() => setFlowToDelete(null)}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>Are you sure?</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary">
            This action cannot be undone. This will permanently delete the flow
            configuration.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button variant="outlined" onClick={() => setFlowToDelete(null)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            color="error"
            onClick={() => flowToDelete && handleDelete(flowToDelete)}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
}
