{"name": "next-js-prj-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:uu-chat-widget": "webpack", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.11.16", "@mui/material": "^5.13.5", "@mui/x-data-grid": "^6.7.0", "@mui/x-date-pickers": "^6.19.4", "@phosphor-icons/react": "^2.0.10", "@types/fernet": "^0.4.3", "axios": "^1.4.0", "eslint": "8.42.0", "eslint-config-next": "13.4.4", "fernet": "^0.3.2", "formik": "^2.4.2", "google-libphonenumber": "^3.2.33", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "linkify-react": "^4.1.1", "linkifyjs": "^4.1.1", "material-icons": "^1.13.12", "moment": "^2.29.4", "mui-nested-menu": "^3.2.2", "next": "^14.2.15", "papaparse": "^5.4.1", "react": "18.2.0", "react-big-calendar": "^1.8.4", "react-color": "^2.19.3", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-grid-gallery": "^1.0.1", "react-grid-heatmap": "^1.3.0", "react-international-phone": "^3.1.2", "react-markdown": "^9.0.1", "react-slick": "^0.29.0", "reactflow": "^11.11.4", "recharts": "^2.10.1", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "slick-carousel": "^1.8.1", "slugify": "^1.6.6", "swr": "^2.2.0", "typescript": "5.1.3", "usehooks-ts": "^3.1.1", "yet-another-react-lightbox": "^3.19.0", "yup": "^1.2.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/google-libphonenumber": "^7.4.27", "@types/gtag.js": "^0.0.18", "@types/node": "^20.6.5", "@types/papaparse": "^5.3.9", "@types/react": "^18.2.22", "@types/react-big-calendar": "^1.8.0", "@types/react-color": "^3.0.9", "@types/react-dom": "^18.2.7", "@types/react-slick": "^0.23.10", "terser": "^5.20.0", "terser-webpack-plugin": "^5.3.9", "ts-loader": "^9.4.4", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}}